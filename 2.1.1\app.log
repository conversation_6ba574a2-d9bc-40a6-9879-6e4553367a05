2025-05-30 23:57:57,713 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-30 23:57:57,718 - WARNING - 主配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml 不存在，正在自动生成默认配置...
2025-05-30 23:57:57,719 - INFO - 正在生成默认主配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:57:57,720 - INFO - 将从 DEFAULT_CONFIG_TEXT 创建或覆盖配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:57:57,744 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:57:57,744 - INFO - 默认主配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:58:12,984 - INFO - API密钥验证成功
2025-05-30 23:58:13,011 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:58:13,012 - INFO - 已将加密的API密钥保存到配置文件
2025-05-30 23:58:13,012 - INFO - 已成功生成默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:58:13,034 - INFO - 🔧 日志系统已初始化，调试模式：关闭
2025-05-30 23:58:13,091 - WARNING - 模式配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml 不存在，正在生成默认配置...
2025-05-30 23:58:13,092 - INFO - 正在生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:58:13,150 - INFO - 将从 DEFAULT_MODE_CONFIG_TEXT 创建或覆盖模式配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:58:13,283 - INFO - 模式配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:58:13,284 - INFO - 默认模式配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:58:13,285 - INFO - 已成功生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:58:13,417 - INFO - 语言模式配置已加载。
2025-05-30 23:58:13,417 - INFO - 语言模式配置已加载。
2025-05-30 23:58:13,418 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-30 23:58:13,418 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-30 23:58:13,431 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-30 23:58:13,443 - INFO - 控制台线程已启动，准备进入循环。
2025-05-30 23:58:13,443 - INFO - 控制台循环开始，准备显示菜单。
2025-05-30 23:58:13,444 - INFO - 菜单已显示，等待用户输入。
2025-05-30 23:58:13,544 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-30 23:58:14,184 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-30 23:58:14,385 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-30 23:58:14,386 - INFO - API服务正常
2025-05-30 23:58:17,363 - INFO - 用户输入: 0
2025-05-30 23:58:23,021 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:58:23,037 - DEBUG - 日志系统已更新：debug_mode=True, log_level=10
2025-05-30 23:58:23,037 - INFO - 🔧 调试模式已开启 - 将显示详细的翻译信息
2025-05-30 23:58:23,038 - INFO - 🧪 测试调试模式功能...
2025-05-30 23:58:23,038 - INFO - 【原文】
这是一个测试文本
2025-05-30 23:58:23,038 - INFO - 【翻译结果】
This is a test text
2025-05-30 23:58:23,038 - INFO - 【API请求JSON】
请求体: {"test": "data"}
2025-05-30 23:58:23,039 - INFO - 发给大模型的完整提示词: 这是测试提示词
2025-05-30 23:58:23,039 - INFO - 检测到三次空格，触发翻译
2025-05-30 23:58:23,039 - INFO - API翻译成功
2025-05-30 23:58:23,039 - INFO - 【缓存命中】使用缓存结果
2025-05-30 23:58:23,039 - INFO - 🧪 调试模式功能测试完成
2025-05-30 23:58:26,749 - INFO - 控制台循环开始，准备显示菜单。
2025-05-30 23:58:26,752 - INFO - 菜单已显示，等待用户输入。
2025-05-30 23:58:45,947 - INFO - 用户输入: q
2025-05-30 23:58:45,947 - INFO - 用户请求退出程序
2025-05-30 23:58:45,947 - INFO - 正在关闭翻译程序...
2025-05-30 23:58:45,947 - INFO - 正在关闭翻译器...
2025-05-30 23:58:45,948 - DEBUG - 重置API状态缓存
2025-05-30 23:58:45,948 - DEBUG - 翻译器资源已释放
2025-05-31 00:14:35,483 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-31 00:14:35,504 - DEBUG - 日志系统已更新：debug_mode=True, log_level=10
2025-05-31 00:14:35,504 - INFO - 🔧 日志系统已初始化，调试模式：开启
2025-05-31 00:14:35,695 - INFO - 语言模式配置已加载。
2025-05-31 00:14:35,696 - INFO - 语言模式配置已加载。
2025-05-31 00:14:35,696 - DEBUG - 创建LRU缓存，容量: 100
2025-05-31 00:14:35,696 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-31 00:14:35,696 - DEBUG - 创建LRU缓存，容量: 50
2025-05-31 00:14:35,696 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-31 00:14:35,701 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-31 00:14:35,701 - DEBUG - Translator: ServiceManager initialized
2025-05-31 00:14:35,701 - DEBUG - Using proactor: IocpProactor
2025-05-31 00:14:35,702 - DEBUG - Translator: Attempting to create main window...
2025-05-31 00:14:35,703 - DEBUG - 初始化翻译器主窗口设置
2025-05-31 00:14:35,703 - DEBUG - 主窗口设置完成
2025-05-31 00:14:35,703 - DEBUG - Translator: Main window created.
2025-05-31 00:14:35,703 - DEBUG - Translator: Attempting to setup keyboard listener...
2025-05-31 00:14:35,703 - DEBUG - 设置键盘监听器
2025-05-31 00:14:35,703 - DEBUG - Translator: Keyboard listener setup.
2025-05-31 00:14:35,704 - DEBUG - Translator: Attempting to start async loop thread...
2025-05-31 00:14:35,704 - DEBUG - Translator: Async loop thread started.
2025-05-31 00:14:35,705 - DEBUG - Translator: __init__ completed.
2025-05-31 00:14:35,714 - INFO - 控制台线程已启动，准备进入循环。
2025-05-31 00:14:35,714 - INFO - 控制台循环开始，准备显示菜单。
2025-05-31 00:14:35,716 - INFO - 菜单已显示，等待用户输入。
2025-05-31 00:14:35,774 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-31 00:14:35,823 - DEBUG - API加密工具已初始化
2025-05-31 00:14:35,824 - DEBUG - API密钥解密成功
2025-05-31 00:14:35,825 - DEBUG - API密钥解密成功
2025-05-31 00:14:35,825 - DEBUG - API密钥解密成功
2025-05-31 00:14:35,825 - DEBUG - API密钥解密成功
2025-05-31 00:14:35,826 - DEBUG - 使用API密钥进行健康检查: AIzaS...
2025-05-31 00:14:35,828 - DEBUG - API密钥解密成功
2025-05-31 00:14:35,829 - DEBUG - API密钥解密成功
2025-05-31 00:14:35,829 - DEBUG - API密钥解密成功
2025-05-31 00:14:35,830 - DEBUG - API密钥解密成功
2025-05-31 00:14:35,830 - DEBUG - 使用API密钥进行健康检查: AIzaS...
2025-05-31 00:14:36,468 - DEBUG - Gemini API密钥验证成功，状态码: 200
2025-05-31 00:14:36,582 - DEBUG - SSL连接清理过程中的预期错误: Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2775)
2025-05-31 00:14:36,583 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 00:14:37,611 - DEBUG - Gemini API密钥验证成功，状态码: 200
2025-05-31 00:14:37,716 - DEBUG - SSL连接清理过程中的预期错误: Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2775)
2025-05-31 00:14:37,717 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 00:14:37,718 - INFO - API服务正常
2025-05-31 00:14:49,722 - INFO - 用户输入: 2
2025-05-31 00:14:49,748 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 00:14:49,764 - DEBUG - 配置验证成功
2025-05-31 00:14:49,765 - DEBUG - 配置保存并验证成功
2025-05-31 00:14:49,765 - INFO - 已切换到翻译模式 2: 中文-韩文-敬语
2025-05-31 00:14:49,880 - INFO - 控制台循环开始，准备显示菜单。
2025-05-31 00:14:49,881 - INFO - 菜单已显示，等待用户输入。
2025-05-31 00:15:35,129 - INFO - 用户输入: 5
2025-05-31 00:15:35,156 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 00:15:35,172 - DEBUG - 配置验证成功
2025-05-31 00:15:35,172 - DEBUG - 配置保存并验证成功
2025-05-31 00:15:35,173 - INFO - 已切换到翻译模式 5: 中文-英文-自然国际化
2025-05-31 00:15:35,288 - INFO - 控制台循环开始，准备显示菜单。
2025-05-31 00:15:35,290 - INFO - 菜单已显示，等待用户输入。
2025-05-31 00:15:56,519 - INFO - 检测到三次空格，触发翻译
2025-05-31 00:15:56,521 - INFO - 【原文】
很高兴认识你~
2025-05-31 00:15:56,523 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 95, 2209.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 00:15:56,523 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.894)]
2025-05-31 00:15:56,523 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-31 00:15:56,524 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-31 00:15:56,524 - INFO - 检测到原文语言: zh
2025-05-31 00:15:56,524 - INFO - 执行正向翻译为: en
2025-05-31 00:15:56,524 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-31 00:15:56,524 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 英文(en)
2025-05-31 00:15:56,524 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [lol|haha|hehe|yeah]
2025-05-31 00:15:56,524 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-31 00:15:56,525 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 英文 ，将内容从 中文 翻译成 英文 ，使用自然国际化
- 若未指定语言或输入既非 中文 也非 英文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 英文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 英文 中等效的 [lol|haha|hehe|yeah] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 英文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 英文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


将以下内容从中文翻译成英文，使用自然国际化：
很高兴认识你~
2025-05-31 00:15:56,525 - DEBUG - 【构建提示词】长度: 616 字符
2025-05-31 00:15:56,525 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-31 00:15:56,532 - INFO - 🔄 显示GUI进度指示器
2025-05-31 00:15:56,760 - DEBUG - API密钥解密成功
2025-05-31 00:15:56,761 - DEBUG - API密钥解密成功
2025-05-31 00:15:56,761 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 00:15:56,762 - DEBUG - 思考预算设置为 0，思考模式已禁用
2025-05-31 00:15:56,762 - DEBUG - 已为模型 gemini-2.5-flash-preview-04-17 禁用思考模式(thinkingBudget=0)
2025-05-31 00:15:56,763 - DEBUG - 【API请求JSON】模型: gemini-2.5-flash-preview-04-17, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 00:15:56,763 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 00:15:56,763 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 中文 和输出为 英文 ，将内容从 中文 翻译成 英文 ，使用自然国际化\n- 若未指定语言或输入既非 中文 也非 英文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 英文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 英文 中等效的 [lol|haha|hehe|yeah] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 英文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 英文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n将以下内容从中文翻译成英文，使用自然国际化：\n很高兴认识你~"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64,
    "thinkingConfig": {
      "thinkingBudget": 0
    }
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 00:15:57,644 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "Nice to meet you!"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 398,
    "candidatesTokenCount": 5,
    "totalTokenCount": 403,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 398
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "rcs5aLPUHf2v1MkPl5r0gA0"
}

2025-05-31 00:15:57,645 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': 'Nice to meet you!'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 398, 'candidatesTokenCount': 5, 'totalTokenCount': 403, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 398}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'rcs5aLPUHf2v1MkPl5r0gA0'}
2025-05-31 00:15:57,645 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-31 00:15:57,645 - DEBUG -   - 思考Token数: 0
2025-05-31 00:15:57,646 - DEBUG -   - 提示Token数: 398
2025-05-31 00:15:57,646 - DEBUG -   - 输出Token数: 5
2025-05-31 00:15:57,646 - DEBUG -   - 总Token数: 403
2025-05-31 00:15:57,647 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('ENGLISH', 'en', 94, 1325.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 00:15:57,647 - DEBUG - 基于特征补充候选: vi (score: 0.3824)
2025-05-31 00:15:57,647 - DEBUG - 基于特征补充候选: fr (score: 0.3824)
2025-05-31 00:15:57,647 - DEBUG - 基于特征补充候选: de (score: 0.3824)
2025-05-31 00:15:57,647 - DEBUG - 基于特征补充候选: es (score: 0.3824)
2025-05-31 00:15:57,647 - DEBUG - 基于特征补充候选: pt (score: 0.3824)
2025-05-31 00:15:57,648 - DEBUG - 基于特征补充候选: it (score: 0.3824)
2025-05-31 00:15:57,648 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('en', 0.887), ('vi', 0.382), ('fr', 0.382), ('de', 0.382), ('es', 0.382), ('pt', 0.382), ('it', 0.382)]
2025-05-31 00:15:57,648 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: en
2025-05-31 00:15:57,648 - DEBUG - 【缓存更新】保存语言检测结果: en
2025-05-31 00:15:57,648 - INFO - 检测到目标语言文本，识别为: en (用于反向翻译)
2025-05-31 00:15:58,104 - DEBUG - 输入框内容已替换为: Nice to meet you!
2025-05-31 00:15:58,106 - INFO - 【翻译结果】
Nice to meet you!
2025-05-31 00:15:58,106 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 00:15:58,107 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-31 00:15:58,107 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-31 00:15:58,109 - INFO - 已立即保存 2 条缓存记录
2025-05-31 00:15:58,110 - INFO - ✅ 隐藏GUI进度指示器
2025-05-31 00:15:58,113 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-31 00:18:33,128 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-31 00:18:33,149 - DEBUG - 日志系统已更新：debug_mode=True, log_level=10
2025-05-31 00:18:33,149 - INFO - 🔧 日志系统已初始化，调试模式：开启
2025-05-31 00:18:33,338 - INFO - 语言模式配置已加载。
2025-05-31 00:18:33,338 - INFO - 语言模式配置已加载。
2025-05-31 00:18:33,339 - DEBUG - 创建LRU缓存，容量: 100
2025-05-31 00:18:33,339 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-31 00:18:33,339 - DEBUG - 创建LRU缓存，容量: 50
2025-05-31 00:18:33,339 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-31 00:18:33,345 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-31 00:18:33,345 - DEBUG - Translator: ServiceManager initialized
2025-05-31 00:18:33,345 - DEBUG - Using proactor: IocpProactor
2025-05-31 00:18:33,345 - DEBUG - Translator: Attempting to create main window...
2025-05-31 00:18:33,346 - DEBUG - 初始化翻译器主窗口设置
2025-05-31 00:18:33,346 - DEBUG - 主窗口设置完成
2025-05-31 00:18:33,346 - DEBUG - Translator: Main window created.
2025-05-31 00:18:33,346 - DEBUG - Translator: Attempting to setup keyboard listener...
2025-05-31 00:18:33,346 - DEBUG - 设置键盘监听器
2025-05-31 00:18:33,346 - DEBUG - Translator: Keyboard listener setup.
2025-05-31 00:18:33,346 - DEBUG - Translator: Attempting to start async loop thread...
2025-05-31 00:18:33,347 - DEBUG - Translator: Async loop thread started.
2025-05-31 00:18:33,347 - DEBUG - Translator: __init__ completed.
2025-05-31 00:18:33,357 - INFO - 控制台线程已启动，准备进入循环。
2025-05-31 00:18:33,358 - INFO - 控制台循环开始，准备显示菜单。
2025-05-31 00:18:33,359 - INFO - 菜单已显示，等待用户输入。
2025-05-31 00:18:33,429 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-31 00:18:33,468 - DEBUG - API加密工具已初始化
2025-05-31 00:18:33,470 - DEBUG - API密钥解密成功
2025-05-31 00:18:33,470 - DEBUG - API密钥解密成功
2025-05-31 00:18:33,471 - DEBUG - API密钥解密成功
2025-05-31 00:18:33,471 - DEBUG - API密钥解密成功
2025-05-31 00:18:33,471 - DEBUG - 使用API密钥进行健康检查: AIzaS...
2025-05-31 00:18:33,474 - DEBUG - API密钥解密成功
2025-05-31 00:18:33,475 - DEBUG - API密钥解密成功
2025-05-31 00:18:33,475 - DEBUG - API密钥解密成功
2025-05-31 00:18:33,476 - DEBUG - API密钥解密成功
2025-05-31 00:18:33,476 - DEBUG - 使用API密钥进行健康检查: AIzaS...
2025-05-31 00:18:34,963 - DEBUG - Gemini API密钥验证成功，状态码: 200
2025-05-31 00:18:35,071 - DEBUG - SSL连接清理过程中的预期错误: Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2775)
2025-05-31 00:18:35,072 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 00:18:35,072 - INFO - API服务正常
2025-05-31 00:18:35,140 - DEBUG - Gemini API密钥验证成功，状态码: 200
2025-05-31 00:18:35,254 - DEBUG - SSL连接清理过程中的预期错误: Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2775)
2025-05-31 00:18:35,255 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 00:18:42,510 - INFO - 用户输入: 2
2025-05-31 00:18:42,537 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 00:18:42,553 - DEBUG - 配置验证成功
2025-05-31 00:18:42,553 - DEBUG - 配置保存并验证成功
2025-05-31 00:18:42,553 - INFO - 已切换到翻译模式 2: 中文-韩文-敬语
2025-05-31 00:18:42,668 - INFO - 控制台循环开始，准备显示菜单。
2025-05-31 00:18:42,670 - INFO - 菜单已显示，等待用户输入。
2025-05-31 00:18:51,639 - INFO - 用户输入: 5
2025-05-31 00:18:51,665 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 00:18:51,683 - DEBUG - 配置验证成功
2025-05-31 00:18:51,684 - DEBUG - 配置保存并验证成功
2025-05-31 00:18:51,684 - INFO - 已切换到翻译模式 5: 中文-英文-自然国际化
2025-05-31 00:18:51,799 - INFO - 控制台循环开始，准备显示菜单。
2025-05-31 00:18:51,801 - INFO - 菜单已显示，等待用户输入。
2025-05-31 00:19:01,763 - INFO - 检测到三次空格，触发翻译
2025-05-31 00:19:01,765 - INFO - 【原文】
很高兴认识你！
2025-05-31 00:19:01,767 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 95, 2209.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 00:19:01,768 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.894)]
2025-05-31 00:19:01,768 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-31 00:19:01,768 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-31 00:19:01,768 - INFO - 检测到原文语言: zh
2025-05-31 00:19:01,769 - INFO - 执行正向翻译为: en
2025-05-31 00:19:01,769 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-31 00:19:01,769 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 英文(en)
2025-05-31 00:19:01,769 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [lol|haha|hehe|yeah]
2025-05-31 00:19:01,770 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-31 00:19:01,770 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 英文 ，将内容从 中文 翻译成 英文 ，使用自然国际化
- 若未指定语言或输入既非 中文 也非 英文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 英文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 英文 中等效的 [lol|haha|hehe|yeah] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 英文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 英文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


将以下内容从中文翻译成英文，使用自然国际化：
很高兴认识你！
2025-05-31 00:19:01,770 - DEBUG - 【构建提示词】长度: 616 字符
2025-05-31 00:19:01,771 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-31 00:19:01,780 - INFO - 🔄 显示GUI进度指示器
2025-05-31 00:19:01,877 - DEBUG - API密钥解密成功
2025-05-31 00:19:01,877 - DEBUG - API密钥解密成功
2025-05-31 00:19:01,877 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-31 00:19:01,878 - DEBUG - 思考预算设置为 0，思考模式已禁用
2025-05-31 00:19:01,878 - DEBUG - 已为模型 gemini-2.5-flash-preview-04-17 禁用思考模式(thinkingBudget=0)
2025-05-31 00:19:01,879 - DEBUG - 【API请求JSON】模型: gemini-2.5-flash-preview-04-17, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-31 00:19:01,879 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-31 00:19:01,879 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 中文 和输出为 英文 ，将内容从 中文 翻译成 英文 ，使用自然国际化\n- 若未指定语言或输入既非 中文 也非 英文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 英文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 英文 中等效的 [lol|haha|hehe|yeah] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 英文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 英文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n将以下内容从中文翻译成英文，使用自然国际化：\n很高兴认识你！"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64,
    "thinkingConfig": {
      "thinkingBudget": 0
    }
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-31 00:19:02,682 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "It's a pleasure to meet you!"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 398,
    "candidatesTokenCount": 9,
    "totalTokenCount": 407,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 398
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "Zsw5aMvgHrOc1MkP49uV8Qk"
}

2025-05-31 00:19:02,683 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': "It's a pleasure to meet you!"}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 398, 'candidatesTokenCount': 9, 'totalTokenCount': 407, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 398}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'Zsw5aMvgHrOc1MkP49uV8Qk'}
2025-05-31 00:19:02,683 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-31 00:19:02,683 - DEBUG -   - 思考Token数: 0
2025-05-31 00:19:02,683 - DEBUG -   - 提示Token数: 398
2025-05-31 00:19:02,683 - DEBUG -   - 输出Token数: 9
2025-05-31 00:19:02,683 - DEBUG -   - 总Token数: 407
2025-05-31 00:19:02,684 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('ENGLISH', 'en', 96, 1536.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-31 00:19:02,685 - DEBUG - 基于特征补充候选: vi (score: 0.3750)
2025-05-31 00:19:02,685 - DEBUG - 基于特征补充候选: fr (score: 0.3750)
2025-05-31 00:19:02,685 - DEBUG - 基于特征补充候选: de (score: 0.3750)
2025-05-31 00:19:02,685 - DEBUG - 基于特征补充候选: es (score: 0.3750)
2025-05-31 00:19:02,685 - DEBUG - 基于特征补充候选: pt (score: 0.3750)
2025-05-31 00:19:02,685 - DEBUG - 基于特征补充候选: it (score: 0.3750)
2025-05-31 00:19:02,685 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('en', 0.897), ('vi', 0.375), ('fr', 0.375), ('de', 0.375), ('es', 0.375), ('pt', 0.375), ('it', 0.375)]
2025-05-31 00:19:02,685 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: en
2025-05-31 00:19:02,686 - DEBUG - 【缓存更新】保存语言检测结果: en
2025-05-31 00:19:02,686 - INFO - 检测到目标语言文本，识别为: en (用于反向翻译)
2025-05-31 00:19:03,144 - DEBUG - 输入框内容已替换为: It's a pleasure to meet you!
2025-05-31 00:19:03,146 - INFO - 【翻译结果】
It's a pleasure to meet you!
2025-05-31 00:19:03,146 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 00:19:03,147 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-31 00:19:03,147 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-31 00:19:03,149 - INFO - 已立即保存 2 条缓存记录
2025-05-31 00:19:03,149 - INFO - 🔄 ✅ 隐藏GUI进度指示器
2025-05-31 00:19:03,152 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
