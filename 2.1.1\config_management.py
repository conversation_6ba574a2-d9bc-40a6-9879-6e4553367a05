import os
import sys
import logging
import yaml
from ruamel.yaml import YAML
from ruamel.yaml.scalarstring import PreservedScalarString
from dataclasses import dataclass, field, asdict, is_dataclass
from typing import Dict, Any, List, Optional, Union, Callable
import io # 用于 StringIO

# 日志配置 (可以简化，或从主程序共享)
logger = logging.getLogger(__name__) # 使用 __name__
logger.setLevel(logging.INFO)
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(module)s - %(message)s")
console_handler.setFormatter(formatter)
if not logger.handlers: # 避免重复添加 handler
    logger.addHandler(console_handler)

# 配置文件路径 (与主程序保持一致)
def get_application_path():
    """获取应用程序路径，兼容打包后的环境"""
    try:
        # 检查是否是 PyInstaller 打包的环境
        if getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS'):
            # PyInstaller 打包的情况
            base_path = os.path.dirname(sys.executable)
            logger.info(f"检测到 PyInstaller 打包环境，使用可执行文件目录: {base_path}")
            return base_path
        else:
            # 正常运行的情况
            script_path = os.path.dirname(os.path.abspath(__file__))
            logger.info(f"使用脚本目录作为应用路径: {script_path}")
            return script_path
    except Exception as e:
        # 发生错误时尝试使用当前工作目录
        fallback_path = os.getcwd()
        logger.error(f"获取应用路径时出错: {e}，使用当前工作目录: {fallback_path}")
        return fallback_path

SCRIPT_DIR = get_application_path()
CONFIG_FILE = os.path.join(SCRIPT_DIR, "config.yaml")
MODE_CONFIG_FILE = os.path.join(SCRIPT_DIR, "mode_config.yaml")
LOG_CONFIG_FILE = os.path.join(SCRIPT_DIR, "log_config.yaml")

# 添加检查路径是否可写的函数
def is_path_writable(path):
    """检查路径是否可写"""
    if not os.path.exists(os.path.dirname(path)):
        try:
            os.makedirs(os.path.dirname(path))
            return True
        except (OSError, PermissionError):
            return False

    # 如果目录存在，检查是否可写
    try:
        test_file = os.path.join(os.path.dirname(path), "_write_test.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        return True
    except (OSError, PermissionError):
        return False

# 默认配置文件内容 (从主程序复制)
DEFAULT_CONFIG_TEXT = '''
# 翻译程序主配置文件
# 版本：2.0.7

# API 配置
api_key: ""  # 必须使用加密格式的API密钥，使用api_crypto.py工具加密
model_id: "gemini-2.5-flash-preview-05-20"  # 使用的主模型ID
gemini_fallback_model_id: "gemini-2.0-flash-lite-preview-02-05" # Gemini API 备用模型ID
openai_fallback_model_id: "gpt-4.1" # OpenAI API 备用模型ID
api_mode: "gemini"  # API 模式，可选 "gemini"（谷歌API） 或 "openai"（OpenAI兼容API）
api_base_url: "https://api.openai.com"  # OpenAI兼容API的基础URL，仅在 api_mode 为 "openai" 时生效
api_endpoint: "/v1/chat/completions"  # OpenAI兼容API的端点，仅在 api_mode 为 "openai" 时生效

# 模型生成参数
temperature: 0.1  # 模型温度，控制生成文本的随机性，范围 0-2，建议 0-1，默认 0.1
top_p: 0.85  # Top-P 核采样值，控制生成文本的多样性，范围 0-1，建议 0.5-1，默认 0.85
max_output_tokens: 1024  # 生成的最大输出标记数，范围 1-4096，默认 1024
top_k: 30  # Top-K采样，范围 1-100，默认 30
frequency_penalty: 0.0 # 频率惩罚，范围 0-2，默认 0.0
presence_penalty: 0.0 # 存在惩罚，范围 0-2，默认 0.0

# 翻译行为配置
translation_mode: 2  # 默认翻译模式编号，对应 mode_config.yaml 中的模式，建议 1-5
max_text_length: 500  # 最大翻译文本长度（字符数），超过此长度将拒绝翻译，建议 100-1000
context_max_count: 8  # 上下文最大数量，用于保持翻译一致性，范围 0-20，默认 8
short_text_threshold: 10  # 短文本阈值，小于此长度的文本使用优化检测逻辑，建议 5-20
lang_detection_threshold: 0.9  # 语言检测置信度阈值，低于此值将使用特征检查，范围 0-1，默认 0.9

# 网络和请求配置
# TCP连接设置
tcp_connector:
  limit: 10  # 限制同时连接数
  ttl_dns_cache: 300  # DNS缓存时间（秒）
  keepalive_timeout: 60  # 保持连接活跃时间（秒）

# 超时设置（秒）
timeout:
  total: 10  # 总超时时间
  connect: 3  # 连接超时
  sock_connect: 10  # 套接字连接超时
  sock_read: 20  # 套接字读取超时

# 网络检查设置
network_check:
  hosts:
    - ******* # 尝试连接的检查主机
  #   - ******* # 可以有多个备用主机
  port: 53  # 网络检查端口
  timeout: 1  # 网络检查超时（秒） - 将这里改为1秒

# API健康检查设置
api_health_check:
  timeout_total: 10  # 总超时（秒）
  timeout_connect: 5  # 连接超时（秒）
  timeout_sock_connect: 5  # 套接字连接超时（秒）
  timeout_sock_read: 8  # 套接字读取超时（秒）

# 网络配置
request_min_interval: 1.0  # 两次翻译请求之间的最小间隔（秒），防止过于频繁请求，建议 0.5-2，默认 1.0

# 日志和调试
debug_mode: false   # 调试模式，true 为开启（显示详细日志），false 为关闭，默认 false
log_info_max: 10  # 日志文件中 INFO 级别的最大条目数，建议 50-200，默认 50
log_other_max: 10  # 日志文件中非 INFO 级别的最大条目数（WARNING/ERROR），建议 10-50，默认 50

# GUI 配置
show_gui_progress: true  # 是否显示GUI等待提示，true为显示，false为不显示

# 文本过滤配置
common_symbols: '[,.!?;:"\'\'()\\[\\]\\{\\}<>+=*/&@#$%^&*~|_，。！？；：、""\'\'（）【】《》]'  # 通用的常用符号集，正则表达式，翻译中保留这些字符
illegal_chars: '[\\x00-\\x1F\\x7F-\\x9F\\씨]'  # 需要移除的非法字符，正则表达式，例如控制字符

# 安全设置
safety_settings:
  gemini:
    - category: HARM_CATEGORY_HARASSMENT
      threshold: BLOCK_NONE
    - category: HARM_CATEGORY_HATE_SPEECH
      threshold: BLOCK_NONE
    - category: HARM_CATEGORY_SEXUALLY_EXPLICIT
      threshold: BLOCK_NONE
    - category: HARM_CATEGORY_DANGEROUS_CONTENT
      threshold: BLOCK_NONE

# 语言检测和翻译配置
language_detection_cache_size: 100  # 语言检测缓存大小
translation_cache_size: 50  # 翻译结果缓存大小
same_language_match_threshold: 0.5  # 检测翻译结果与原文相似度的阈值，范围0-1，越高检测越严格，默认0.5

# 语言检测和消歧配置
language_detection:
  ambiguity_factor: 1.4  # 语言检测歧义判断系数，范围1.0-2.0，越大越容易区分语言
  hint_bias: 0.2  # 语言提示偏置值，范围0.0-0.5，越大越信任提示语言
  prob_weight: 0.7  # 主检测器权重，范围0.0-1.0
  feature_weight: 0.3  # 特征匹配权重，范围0.0-1.0
  short_text_prob_weight: 0.4  # 短文本主检测器权重，范围0.0-1.0
  short_text_feature_weight: 0.6  # 短文本特征匹配权重，范围0.0-1.0
  min_char_threshold: 10  # 最小字符检查阈值

# 字符比例配置
translation_quality:
  min_char_ratio: 0.2  # 默认最小字符比例阈值，翻译结果字符数不应少于原文的这一比例
  default_feature_dominance_ratio: 2.0  # 默认特征主导比例，用于语言对消歧

# 语言家族分组
language_families:
  cjk:  # 中日韩语系
    - zh
    - ja
    - ko
  european:  # 欧洲语系
    - en
    - fr
    - de
    - es
    - it
    - pt
    - ru
  indic:  # 印度语系
    - hi
    - bn
    - ur
  southeast_asian:  # 东南亚语系
    - th
    - vi
    - id
    - ms
    - km
    - lo
  semitic:  # 闪米特语系
    - ar
    - he

language_specific_settings: {}  # 每种语言的特定设置

# 通用语气符号配置
universal_punctuation:
  question_marks:
    universal:  # 所有语言通用的问号
      - "?"
      - "？"
    latin:  # 拉丁语系
      - "?"
    cjk:  # 中日韩
      - "？"
  exclamation_marks:
    universal:
      - "!"
      - "！"
    latin:
      - "!"
    cjk:
      - "！"
'''

# 默认模式配置文本内容（YAML格式）
DEFAULT_MODE_CONFIG_TEXT = '''
# 语言模式配置文件
# 版本：2.0.7

# 语气助词配置
tone_particles:
  zh: "[哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]"  # 中文语气助词
  ko: "[ㅋ|ㅎ|아|네|헤|ㅜ]"  # 韩文语气助词
  ja: "[ｗ笑]"  # 日文语气助词
  en: "[lol|haha|hehe|yeah]"  # 英文语气助词
  vi: "[hihi|hehe|ạ|nhé|nha|á|ấy]"  # 越南语气助词
  fr: "[lol|haha|hein|quoi|eh bien]"  # 法语语气助词
  de: "[haha|hehe|ne|ja|doch|mal]"  # 德语语气助词
  es: "[jaja|jeje|eh|pues|vale|no]"  # 西班牙语语气助词
  ru: "[хаха|хехе|ну|да|же]"  # 俄语语气助词
  pt: "[haha|hehe|né|pois|então]"  # 葡萄牙语语气助词
  ar: "[هههه|والله|يعني|طيب]"  # 阿拉伯语语气助词
  it: "[haha|hehe|eh|beh|dai|cioè]"  # 意大利语语气助词
  th: "[ฮ่าฮ่า|ฮิฮิ|นะ|ค่ะ|ครับ|จ้า|ล่ะ]"  # 泰语语气助词
  km: "[ហាហា|ហិហិ|ណា|ទេ|ហើយ]"  # 柬埔寨语语气助词

# 翻译模式配置
translation_modes:
  1:  # 模式1：中韩平语
    source_lang: 中文
    target_lang: 韩文
    style: 平语
    default_lang: 中文
    source_code: zh
    target_code: ko
  2:  # 模式2：中韩敬语
    source_lang: 中文
    target_lang: 韩文
    style: 敬语
    default_lang: 中文
    source_code: zh
    target_code: ko
  3:  # 模式3：中日敬语
    source_lang: 中文
    target_lang: 日文
    style: 敬语
    default_lang: 中文
    source_code: zh
    target_code: ja
  4:  # 模式4：中日平语
    source_lang: 中文
    target_lang: 日文
    style: 平语
    default_lang: 中文
    source_code: zh
    target_code: ja
  5:  # 模式5：中英
    source_lang: 中文
    target_lang: 英文
    style: "自然国际化"
    default_lang: 中文
    source_code: zh
    target_code: en
  6:  # 模式6：中越
    source_lang: 中文
    target_lang: 越南文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: vi
  7:  # 模式7：中法
    source_lang: 中文
    target_lang: 法文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: fr
  8:  # 模式8：中德
    source_lang: 中文
    target_lang: 德文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: de
  9:  # 模式9：中西
    source_lang: 中文
    target_lang: 西班牙文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: es
  10:  # 模式10：中俄
    source_lang: 中文
    target_lang: 俄文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: ru
  11:  # 模式11：中葡
    source_lang: 中文
    target_lang: 葡萄牙文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: pt
  12:  # 模式12：中阿
    source_lang: 中文
    target_lang: 阿拉伯文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: ar
  13:  # 模式13：中意
    source_lang: 中文
    target_lang: 意大利文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: it
  14:  # 模式14：中泰
    source_lang: 中文
    target_lang: 泰文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: th
  15:  # 模式15：中柬
    source_lang: 中文
    target_lang: 柬埔寨文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: km

# 语言特征配置
language_features:
  zh:  # 中文特征
    pattern: "[\\u4E00-\\u9FFF]"  # 汉字Unicode范围
    exclusive:  # 排除特征
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 汉字  # 描述
    question_pattern: "[?？]"
    exclamation_pattern: "[!！]"
  ko:  # 韩文特征
    pattern: "[\\uAC00-\\uD7AF]"  # 韩文谚文Unicode范围
    exclusive:
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 韩文谚文  # 描述
    question_pattern: "[?？]"
    exclamation_pattern: "[!！]"
  ja:  # 日文特征
    pattern: "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 日文假名Unicode范围
    exclusive:
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
    desc: 日文假名  # 描述
    question_pattern: "[?？]"
    exclamation_pattern: "[!！]"
  en:  # 英文特征
    pattern: "[A-Za-z]"  # 英文字母范围
    exclusive:  # 排除特征
      - "[\\u4E00-\\u9FFF]"  # 排除中文
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 英文拉丁字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
  vi:  # 越南文特征
    pattern: "[A-Za-zÀ-ỹ]"  # 越南文字母范围（包括带音调符号的拉丁字母）
    exclusive:  # 排除特征
      - "[\\u4E00-\\u9FFF]"  # 排除中文
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 越南文拉丁字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
  fr:  # 法文特征
    pattern: "[A-Za-zÀ-ÿ]"  # 法文字母范围（包括带音调符号的拉丁字母）
    exclusive:  # 排除特征
      - "[\\u4E00-\\u9FFF]"  # 排除中文
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 法文拉丁字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
  de:  # 德文特征
    pattern: "[A-Za-zÄäÖöÜüß]"  # 德文字母范围（包括变音符号）
    exclusive:  # 排除特征
      - "[\\u4E00-\\u9FFF]"  # 排除中文
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 德文拉丁字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
  es:  # 西班牙文特征
    pattern: "[A-Za-zÁáÉéÍíÓóÚúÜüÑñ]"  # 西班牙文字母范围
    exclusive:  # 排除特征
      - "[\\u4E00-\\u9FFF]"  # 排除中文
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 西班牙文拉丁字母  # 描述
    question_pattern: "[?¿]"
    exclamation_pattern: "[!¡]"
  ru:  # 俄文特征
    pattern: "[А-Яа-я]"  # 俄文西里尔字母范围
    exclusive:  # 排除特征
      - "[\\u4E00-\\u9FFF]"  # 排除中文
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 俄文西里尔字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
  pt:  # 葡萄牙文特征
    pattern: "[A-Za-zÁáÂâÃãÀàÇçÉéÊêÍíÓóÔôÕõÚú]"  # 葡萄牙文字母范围
    exclusive:  # 排除特征
      - "[\\u4E00-\\u9FFF]"  # 排除中文
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 葡萄牙文拉丁字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
  ar:  # 阿拉伯文特征
    pattern: "[\\u0600-\\u06FF]"  # 阿拉伯文字母范围
    exclusive:  # 排除特征
      - "[\\u4E00-\\u9FFF]"  # 排除中文
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 阿拉伯文字母  # 描述
    question_pattern: "[؟]"
    exclamation_pattern: "[!]"
  it:  # 意大利文特征
    pattern: "[A-Za-zÀàÈèÉéÌìÍíÒòÓóÙùÚú]"  # 意大利文字母范围
    exclusive:  # 排除特征
      - "[\\u4E00-\\u9FFF]"  # 排除中文
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 意大利文拉丁字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
  th:  # 泰文特征
    pattern: "[\\u0E00-\\u0E7F]"  # 泰文字母范围
    exclusive:  # 排除特征
      - "[\\u4E00-\\u9FFF]"  # 排除中文
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 泰文字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
  km:  # 柬埔寨文特征
    pattern: "[\\u1780-\\u17FF]"  # 柬埔寨文字母范围
    exclusive:  # 排除特征
      - "[\\u4E00-\\u9FFF]"  # 排除中文
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 柬埔寨文字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"

# 特殊语言组配置
special_language_groups:
  cjk:
    languages:
      - ja
      - zh
      - ko
    strict_detection: false
    desc: 中日韩语言组
  latin:
    languages:
      - en
      - es
      - fi
      - sv
      - de
      - en
      - nl
      - da
      - pt
      - no
      - vi
      - it
      - fr
    strict_detection: false
    desc: 拉丁语系
  indic:
    languages:
      - ml
      - mr
      - ta
      - te
      - bn
      - gu
      - kn
      - hi
      - pa
    strict_detection: false
    desc: 印度语系
  semitic:
    languages:
      - he
      - am
      - ti
      - mt
      - ar
    strict_detection: false
    desc: 闪米特语系
  southeast_asian:
    languages:
      - vi
      - th
      - lo
      - km
      - my
      - id
      - ms
      - tl
    strict_detection: false
    desc: 东南亚语系

# 特殊语言对配置
special_language_pairs:
  "*-*":
    max_attempts: 2
    desc: 通用语言对配置
    skip_source_detection: false
    min_char_ratio: 0.2
  zh-ko:
    max_attempts: 2
    desc: zh-ko互译配置
    skip_source_detection: true
    min_char_ratio: 0.3
  zh-ja:
    max_attempts: 2
    desc: zh-ja互译配置
    skip_source_detection: true
    min_char_ratio: 0.3
  zh-en:
    max_attempts: 2
    desc: zh-en互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-vi:
    max_attempts: 2
    desc: zh-vi互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-fr:
    max_attempts: 2
    desc: zh-fr互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-de:
    max_attempts: 2
    desc: zh-de互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-es:
    max_attempts: 2
    desc: zh-es互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-ru:
    max_attempts: 2
    desc: zh-ru互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-pt:
    max_attempts: 2
    desc: zh-pt互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-ar:
    max_attempts: 2
    desc: zh-ar互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-it:
    max_attempts: 2
    desc: zh-it互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-th:
    max_attempts: 2
    desc: zh-th互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-km:
    max_attempts: 2
    desc: zh-km互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  cjk-latin:
    desc: cjk与latin语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  cjk-slavic:
    desc: cjk与slavic语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  cjk-indic:
    desc: cjk与indic语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  cjk-semitic:
    desc: cjk与semitic语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  latin-cjk:
    desc: latin与cjk语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  latin-slavic:
    desc: latin与slavic语言组互译配置
    min_char_ratio: 0.1
  latin-indic:
    desc: latin与indic语言组互译配置
    min_char_ratio: 0.1
  latin-semitic:
    desc: latin与semitic语言组互译配置
    min_char_ratio: 0.1
  slavic-cjk:
    desc: slavic与cjk语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  slavic-latin:
    desc: slavic与latin语言组互译配置
    min_char_ratio: 0.1
  slavic-indic:
    desc: slavic与indic语言组互译配置
    min_char_ratio: 0.1
  slavic-semitic:
    desc: slavic与semitic语言组互译配置
    min_char_ratio: 0.1
  indic-cjk:
    desc: indic与cjk语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  indic-latin:
    desc: indic与latin语言组互译配置
    min_char_ratio: 0.1
  indic-slavic:
    desc: indic与slavic语言组互译配置
    min_char_ratio: 0.1
  indic-semitic:
    desc: indic与semitic语言组互译配置
    min_char_ratio: 0.1
  semitic-cjk:
    desc: semitic与cjk语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  semitic-latin:
    desc: semitic与latin语言组互译配置
    min_char_ratio: 0.1
  semitic-slavic:
    desc: semitic与slavic语言组互译配置
    min_char_ratio: 0.1
  semitic-indic:
    desc: semitic与indic语言组互译配置
    min_char_ratio: 0.1
  cjk-*:
    desc: cjk语言组对外互译
    min_char_ratio: 0.15
    max_attempts: 2
  "*-cjk":
    desc: 外部语言翻译到cjk语言组
    min_char_ratio: 0.15
    max_attempts: 2
  latin-*:
    desc: latin语言组对外互译
    min_char_ratio: 0.15
    max_attempts: 2
  "*-latin":
    desc: 外部语言翻译到latin语言组
    min_char_ratio: 0.15
    max_attempts: 2
  slavic-*:
    desc: slavic语言组对外互译
    min_char_ratio: 0.15
    max_attempts: 2
  "*-slavic":
    desc: 外部语言翻译到slavic语言组
    min_char_ratio: 0.15
    max_attempts: 2
  indic-*:
    desc: indic语言组对外互译
    min_char_ratio: 0.15
    max_attempts: 2
  "*-indic":
    desc: 外部语言翻译到indic语言组
    min_char_ratio: 0.15
    max_attempts: 2
  semitic-*:
    desc: semitic语言组对外互译
    min_char_ratio: 0.15
    max_attempts: 2
  "*-semitic":
    desc: 外部语言翻译到semitic语言组
    min_char_ratio: 0.15
    max_attempts: 2
  cjk-southeast_asian:
    desc: cjk与东南亚语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  southeast_asian-cjk:
    desc: 东南亚语言组与cjk互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  latin-southeast_asian:
    desc: latin与东南亚语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  southeast_asian-latin:
    desc: 东南亚语言组与latin互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  slavic-southeast_asian:
    desc: slavic与东南亚语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  southeast_asian-slavic:
    desc: 东南亚语言组与slavic互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  indic-southeast_asian:
    desc: indic与东南亚语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  southeast_asian-indic:
    desc: 东南亚语言组与indic互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  semitic-southeast_asian:
    desc: semitic与东南亚语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  southeast_asian-semitic:
    desc: 东南亚语言组与semitic互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  southeast_asian-*:
    desc: 东南亚语言组对外互译
    min_char_ratio: 0.15
    max_attempts: 2
  "*-southeast_asian":
    desc: 外部语言翻译到东南亚语言组
    min_char_ratio: 0.15
    max_attempts: 2
'''

# 默认日志配置文本内容（YAML格式）
DEFAULT_LOG_CONFIG_TEXT = '''
# 日志配置文件
# 用于控制翻译软件的日志输出详细程度和格式

# 日志级别设置
log_level: INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# 日志显示设置
log_display:
  # 是否启用彩色输出
  enable_colors: true

  # 是否显示图标
  show_icons: true

  # 时间格式
  time_format: "%H:%M:%S"  # 简化时间格式，只显示时分秒

  # 消息最大长度（超过会被截断）
  max_message_length: 120

  # 是否显示模块名称
  show_module: false

  # 日志类别颜色配置
  level_colors:
    DEBUG: "\\033[36m"     # 青色
    INFO: "\\033[32m"      # 绿色
    WARNING: "\\033[33m"   # 黄色
    ERROR: "\\033[31m"     # 红色
    CRITICAL: "\\033[35m"  # 紫色

  # 日志类别图标配置
  category_icons:
    翻译: "🔄"
    检测: "🔍"
    缓存: "💾"
    API: "🌐"
    配置: "⚙️"
    网络: "📡"
    错误: "❌"
    成功: "✅"
    警告: "⚠️"

  # 关键词高亮
  key_highlights:
    - "缓存"
    - "API"
    - "翻译"
    - "错误"
    - "检测"
    - "网络"
    - "配置"

# 日志过滤设置
log_filter:
  # 详细程度级别: minimal, normal, detailed, debug
  verbosity_level: normal

  # 在 minimal 级别下过滤的消息模式
  minimal_skip_patterns:
    - "【API响应原始文本】"
    - "【API响应JSON对象】"
    - "【构建提示词】"
    - "pycld2 检测结果"
    - "决策逻辑："
    - "基于特征补充候选"
    - "Token使用情况"
    - "当前模型温度:"
    - "替换操作过于频繁"
    - "记录上次翻译目标语言"
    - "【内存缓存更新】"
    - "【本地缓存更新】"
    - "已立即保存"
    - "输入框内容已替换为:"
    - "思考模式已禁用"
    - "明确禁用思考模式"
    - "发给大模型的完整提示词:"
    - "使用缓存的网络状态:"
    - "优先查询本地缓存"
    - "使用API密钥进行翻译请求:"

  # 在 normal 级别下额外过滤的消息模式
  normal_skip_patterns:
    - "API密钥解密成功"
    - "创建LRU缓存"
    - "缓存命中率"

  # 在 detailed 级别下额外过滤的消息模式
  detailed_skip_patterns: []

# 文件日志设置
file_logging:
  # 是否启用文件日志
  enabled: true

  # 日志文件路径
  log_file: "app.log"

  # 文件日志级别
  file_log_level: INFO

  # 文件大小限制（字节）
  max_bytes: 2097152  # 2MB

  # 备份文件数量
  backup_count: 3

  # 文件编码
  encoding: "utf-8"

  # 文件日志格式（不包含颜色和图标）
  file_format: "%(asctime)s - %(levelname)s - %(message)s"

# 特殊日志处理
special_handling:
  # 翻译结果是否单独显示
  highlight_translation_result: true

  # 错误消息是否加粗显示
  bold_errors: true

  # 是否在翻译开始和结束时显示分隔线
  show_translation_separators: false

  # 是否显示翻译统计信息
  show_translation_stats: false

# 调试模式设置
debug_settings:
  # 是否显示详细的API调用信息
  show_api_details: false

  # 是否显示完整的提示词内容
  show_full_prompt: false

  # 是否显示模型响应的原始内容
  show_raw_response: false

  # 是否显示缓存操作详情
  show_cache_details: false
'''

# Config dataclass (从主程序复制)
@dataclass
class Config:
    api_key: str
    model_id: str
    gemini_fallback_model_id: str
    openai_fallback_model_id: str
    api_mode: str
    api_base_url: str
    api_endpoint: str
    # 模型生成参数
    temperature: float
    top_p: float
    max_output_tokens: int
    top_k: int
    frequency_penalty: float
    presence_penalty: float
    thinking_budget_tokens: int # 新增：思考预算Token数量 (Vertex AI Gemini)，0表示关闭思考模式
    # 翻译行为配置
    translation_mode: int
    max_text_length: int
    context_max_count: int
    short_text_threshold: int
    lang_detection_threshold: float
    # 网络和请求配置
    tcp_connector: Dict
    timeout: Dict
    network_check: Dict
    api_health_check: Dict # 新增此行，用于API健康检查配置
    # 网络配置
    request_min_interval: float
    # 日志和调试
    debug_mode: bool
    log_info_max: int
    log_other_max: int
    # GUI配置
    show_gui_progress: bool
    # 文本过滤配置
    common_symbols: str
    illegal_chars: str
    # 安全设置
    safety_settings: Dict
    # 语言相关新增配置
    language_detection_cache_size: int = 100  # 语言检测缓存大小
    translation_cache_size: int = 50  # 翻译结果缓存大小
    same_language_match_threshold: float = 0.5  # 检测翻译结果与原文相似度的阈值
    # 本地缓存配置
    use_local_cache: bool = True  # 是否启用本地缓存
    cache_priority: bool = True  # True=优先使用缓存，False=优先使用大模型
    local_cache_path: str = "translation_cache.db"  # 缓存文件路径
    cache_max_entries: int = 1000  # 最大缓存条目数
    cache_write_delay: float = 2.0  # 缓存写入延迟（秒）
    cache_auto_save: bool = True  # 是否自动保存缓存
    cache_max_queue_size: int = 1000  # 新增：缓存队列最大长度
    cache_write_interval: float = 0.5  # 新增：缓存批量写入间隔（秒）
    cache_retry_times: int = 3  # 新增：缓存失败重试次数
    language_families: Dict = field(default_factory=lambda: {
        "cjk": ["zh", "ja", "ko"],
        "european": ["en", "fr", "de", "es", "it", "pt", "ru"],
        "indic": ["hi", "bn", "ur"],
        "southeast_asian": ["th", "vi", "id", "ms", "km", "lo"], # 新增
        "semitic": ["ar", "he"]                                # 新增
    })  # 语言家族分组
    language_specific_settings: Dict = field(default_factory=dict)
    # 通用语气符号配置
    universal_punctuation: Dict = field(default_factory=lambda: {
        "question_marks": {
            "universal": ["?", "？"],
            "latin": ["?"],
            "cjk": ["？"],
        },
        "exclamation_marks": {
            "universal": ["!", "！"],
            "latin": ["!"],
            "cjk": ["！"],
        }
    })
    # 语言检测和消歧配置
    language_detection: Dict = field(default_factory=lambda: { # 更新 language_detection 默认值
        "ambiguity_factor": 1.4,
        "hint_bias": 0.2,
        "prob_weight": 0.7,
        "feature_weight": 0.3,
        "short_text_prob_weight": 0.4,
        "short_text_feature_weight": 0.6,
        "min_char_threshold": 10
    })
    # 中韩文区分配置 (新增)
    ko_zh_detection: Dict = field(default_factory=lambda: {
        "enabled": True,
        "ko_specific_ratio_threshold": 0.3,
        "lang_feature_score_threshold": 0.3,
        "feature_dominance_ratio": 2.0,
        "cjk_feature_score_threshold": 0.35
    })
    # 翻译质量配置
    translation_quality: Dict = field(default_factory=lambda: {
        "min_char_ratio": 0.2,
        "default_feature_dominance_ratio": 2.0
    })

    def get(self, key: str, default: Any = None) -> Any:
        try:
            value = getattr(self, key, default)
            return value
        except Exception as e:
            logger.error(f"获取配置项 {key} 时出错: {e}")
            return default

# 主程序中的API加解密相关全局变量和函数，移到这里会更独立
# 但当前脚本主要负责生成和保存，不直接进行加密，所以暂时不移动 ApiCrypto 类本身
# init_api_crypto 和 get_real_api_key 可能会被 load_config 调用，但新版 load_config 不会创建文件了

# --- 辅助函数 ---
def _to_yaml_compatible(data: Any) -> Any:
    if isinstance(data, dict):
        return {k: _to_yaml_compatible(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [_to_yaml_compatible(item) for item in data]
    elif isinstance(data, str) and "\n" in data: # ruamel.yaml 需要显式处理换行
        return PreservedScalarString(data)
    return data

def _merge_ruamel_data(target: Any, source: Any):
    if isinstance(source, dict):
        if not isinstance(target, dict):
            return _to_yaml_compatible(source)
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                _merge_ruamel_data(target[key], value)
            else:
                target[key] = _to_yaml_compatible(value)
        return target
    elif isinstance(source, list):
        return _to_yaml_compatible(source) # 列表通常直接替换
    else:
        return _to_yaml_compatible(source)

def deep_merge_configs(base: Dict, update: Dict) -> Dict:
    for key, value in update.items():
        if isinstance(value, dict) and key in base and isinstance(base[key], dict):
            base[key] = deep_merge_configs(base[key], value)
        else:
            base[key] = value
    return base

# --- config.yaml 相关函数 ---
def get_default_config_dict() -> Dict:
    """获取默认配置字典"""
    return {
        # API 配置
        "api_key": "",  # 必须使用加密格式的API密钥
        "model_id": "gemini-2.5-flash-preview-04-17",
        "gemini_fallback_model_id": "gemini-2.0-flash-lite-preview-02-05",
        "openai_fallback_model_id": "gpt-4o",
        "api_mode": "gemini",
        "api_base_url": "https://api.openai.com",
        "api_endpoint": "/v1/chat/completions",

        # 模型生成参数
        "temperature": 0.1,
        "top_p": 0.85,
        "max_output_tokens": 2048,
        "top_k": 64,
        "frequency_penalty": 0.0,
        "presence_penalty": 0.0,
        "thinking_budget_tokens": 0, # 思考预算Token数量 (Vertex AI Gemini)，0表示关闭思考模式

        # 翻译行为配置
        "translation_mode": 1,
        "max_text_length": 500,
        "context_max_count": 8,
        "short_text_threshold": 10,
        "lang_detection_threshold": 0.9,
        "same_language_match_threshold": 0.5,
        # 网络和请求配置
        "tcp_connector": {
            "limit": 10,
            "ttl_dns_cache": 300,
            "keepalive_timeout": 60
        },
        "timeout": {
            "total": 30,
            "connect": 10,
            "sock_connect": 10,
            "sock_read": 20
        },
        "network_check": {
            "hosts": ["*******", "*******"],
            "port": 53,
            "timeout": 1
        },
        "api_health_check": { # 新增 api_health_check
            "timeout_total": 10,
            "timeout_connect": 5,
            "timeout_sock_connect": 5,
            "timeout_sock_read": 8
        },
        # 网络配置
        "request_min_interval": 1.0,
        # 日志和调试
        "debug_mode": False,
        "log_info_max": 50,
        "log_other_max": 50,
        # GUI配置
        "show_gui_progress": True,
        # 文本过滤配置
        "common_symbols": r'[,.!?;:"\'\'()[\]\{\}<>+=*/&@#$%^&*~|_，。！？；：、""\'\'（）【】《》]',
        "illegal_chars": r'[\x00-\x1F\x7F-\x9F]',
        # 安全设置
        "safety_settings": {
            "gemini": [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
            ]
        },
        # 语言相关新增配置
        "language_detection_cache_size": 100,
        "translation_cache_size": 50,
        # 本地缓存配置
        "use_local_cache": True,
        "cache_priority": True,
        "local_cache_path": "translation_cache.db",
        "cache_max_entries": 1000,
        "cache_write_delay": 2.0,
        "cache_auto_save": True,
        "language_families": {
            "cjk": ["zh", "ja", "ko"],
            "european": ["en", "fr", "de", "es", "it", "pt", "ru"],
            "indic": ["hi", "bn", "ur"],
            "southeast_asian": ["th", "vi", "id", "ms", "km", "lo"],
            "semitic": ["ar", "he"]
        },
        "language_specific_settings": {},
        "universal_punctuation": {
            "question_marks": {
                "universal": ["?", "？"],
                "latin": ["?"],
                "cjk": ["？"],
            },
            "exclamation_marks": {
                "universal": ["!", "！"],
                "latin": ["!"],
                "cjk": ["！"],
            }
        },
        "language_detection": {
            "ambiguity_factor": 1.4,
            "hint_bias": 0.2,
            "prob_weight": 0.7,
            "feature_weight": 0.3,
            "short_text_prob_weight": 0.4,
            "short_text_feature_weight": 0.6,
            "min_char_threshold": 10
        },
        "ko_zh_detection": { # 新增 ko_zh_detection
            "enabled": True,
            "ko_specific_ratio_threshold": 0.3,
            "lang_feature_score_threshold": 0.3,
            "feature_dominance_ratio": 2.0,
            "cjk_feature_score_threshold": 0.35
        },
        "translation_quality": { # 新增 translation_quality
            "min_char_ratio": 0.2,
            "default_feature_dominance_ratio": 2.0
        }
    }

def save_main_config(config_data: Union[Dict, Config], filename: str = CONFIG_FILE) -> bool:
    config_dict = config_data if isinstance(config_data, dict) else asdict(config_data)

    # 检查目标路径是否可写
    if not is_path_writable(filename):
        # 尝试使用用户目录作为备用路径
        user_home = os.path.expanduser("~")
        app_data_dir = os.path.join(user_home, ".multitranslator")
        try:
            if not os.path.exists(app_data_dir):
                os.makedirs(app_data_dir)
            backup_filename = os.path.join(app_data_dir, os.path.basename(filename))
            logger.warning(f"无法写入 {filename}，将使用备用路径: {backup_filename}")
            filename = backup_filename
        except Exception as e:
            logger.error(f"创建备用配置目录失败: {e}")
            return False

    yaml_loader = YAML()
    yaml_loader.preserve_quotes = True
    yaml_loader.indent(mapping=2, sequence=4, offset=2)

    try:
        data_to_save = None
        if os.path.exists(filename) and os.path.getsize(filename) > 0:
            try:
                with open(filename, "r", encoding="utf-8-sig") as f:
                    data_to_save = yaml_loader.load(f)
                if not isinstance(data_to_save, dict):
                    logger.warning(f"配置文件 {filename} 内容格式不正确，将使用新配置覆盖。")
                    data_to_save = None
            except Exception as e:
                logger.error(f"读取配置文件 {filename} 失败: {e}。将尝试使用新配置。")
                data_to_save = None

        if data_to_save is None: # 文件不存在，为空，或加载失败，则基于默认文本创建
            logger.info(f"将从 DEFAULT_CONFIG_TEXT 创建或覆盖配置: {filename}")
            try:
                default_config_stream = io.StringIO(DEFAULT_CONFIG_TEXT)
                data_to_save = yaml_loader.load(default_config_stream)
                if not isinstance(data_to_save, dict):
                     logger.error(f"DEFAULT_CONFIG_TEXT 解析失败，无法创建带注释的默认配置。")
                     data_to_save = get_default_config_dict()
            except Exception as e_load_default:
                logger.error(f"从 DEFAULT_CONFIG_TEXT 加载默认配置失败: {e_load_default}。")
                data_to_save = get_default_config_dict()

        # 合并传入的 config_dict 到 data_to_save (它可能是 CommentedMap)
        if isinstance(data_to_save, dict):
            _merge_ruamel_data(data_to_save, config_dict)
        else: # 如果 data_to_save 仍然不是字典 (例如解析失败)，则直接使用传入的 config_dict
             data_to_save = config_dict

        # 确保目录存在
        os.makedirs(os.path.dirname(filename), exist_ok=True)

        # 使用 utf-8-sig 编码保存文件，添加 BOM 头
        with open(filename, "w", encoding="utf-8-sig") as f:
            yaml_loader.dump(data_to_save, f)

        logger.info(f"主配置文件已保存: {filename}")
        return True
    except Exception as e:
        logger.error(f"保存主配置文件 {filename} 失败: {e}", exc_info=True)
        # 备用保存 (简单yaml)
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(filename), exist_ok=True)

            with open(filename, 'w', encoding="utf-8-sig") as file:
                yaml.dump(config_dict, file, allow_unicode=True, default_flow_style=False, sort_keys=False, indent=2)
            logger.info(f"主配置已通过备用方法 (PyYAML) 保存到 {filename}")
            return True
        except Exception as e2:
            logger.error(f"主配置备用保存方式 (PyYAML) 也失败: {e2}")
            return False

def generate_default_main_config(force_overwrite: bool = False):
    # 检查目标路径是否可写
    if not is_path_writable(CONFIG_FILE):
        user_home = os.path.expanduser("~")
        app_data_dir = os.path.join(user_home, ".multitranslator")
        try:
            os.makedirs(app_data_dir, exist_ok=True)
            backup_config_file = os.path.join(app_data_dir, os.path.basename(CONFIG_FILE))
            logger.warning(f"无法写入 {CONFIG_FILE}，将使用备用路径: {backup_config_file}")

            # 如果存在备用文件且不强制覆盖，则直接返回
            if not force_overwrite and os.path.exists(backup_config_file):
                logger.info(f"备用配置文件 {backup_config_file} 已存在。如需覆盖，请使用 --force 参数。")
                return True

            if save_main_config(get_default_config_dict(), backup_config_file):
                logger.info(f"默认主配置文件已成功生成到备用位置: {backup_config_file}")
                return True
            else:
                logger.error(f"无法在备用位置生成默认主配置文件")
                return False
        except Exception as e:
            logger.error(f"创建备用配置失败: {e}")
            return False

    if not force_overwrite and os.path.exists(CONFIG_FILE):
        logger.info(f"{CONFIG_FILE} 已存在。如需覆盖，请使用 --force 参数。")
        return False

    logger.info(f"正在生成默认主配置文件: {CONFIG_FILE}")
    default_config_data = get_default_config_dict()
    if not default_config_data: # 如果从 DEFAULT_CONFIG_TEXT 解析失败
        logger.error("无法获取默认主配置内容，生成失败。")
        return False

    # 对于首次生成，api_key 应该是空的
    default_config_data["api_key"] = ""

    if save_main_config(default_config_data, CONFIG_FILE):
        logger.info(f"默认主配置文件已成功生成: {CONFIG_FILE}")

        # 提示用户输入加密的API密钥
        api_key = prompt_for_api_key()
        if api_key:
            # 更新配置并保存
            default_config_data["api_key"] = api_key
            if save_main_config(default_config_data, CONFIG_FILE):
                logger.info("已将加密的API密钥保存到配置文件")
            else:
                logger.error("保存API密钥到配置文件失败")

        return True
    else:
        logger.error(f"生成默认主配置文件失败: {CONFIG_FILE}")
        return False

# --- log_config.yaml 相关函数 ---
def get_default_log_config_dict() -> Dict:
    """获取默认日志配置字典"""
    yaml_loader = YAML()
    try:
        config_dict = yaml_loader.load(DEFAULT_LOG_CONFIG_TEXT)
        if not isinstance(config_dict, dict):
            logger.error("DEFAULT_LOG_CONFIG_TEXT 解析失败，返回空字典。")
            return {}
        return config_dict
    except Exception as e:
        logger.error(f"解析 DEFAULT_LOG_CONFIG_TEXT 失败: {e}，返回空字典。")
        return {}

def save_log_config_file(log_config_data: Dict, filename: str = LOG_CONFIG_FILE) -> bool:
    """保存日志配置文件"""
    # 检查目标路径是否可写
    if not is_path_writable(filename):
        # 尝试使用用户目录作为备用路径
        user_home = os.path.expanduser("~")
        app_data_dir = os.path.join(user_home, ".multitranslator")
        try:
            if not os.path.exists(app_data_dir):
                os.makedirs(app_data_dir)
            backup_filename = os.path.join(app_data_dir, os.path.basename(filename))
            logger.warning(f"无法写入 {filename}，将使用备用路径: {backup_filename}")
            filename = backup_filename
        except Exception as e:
            logger.error(f"创建备用配置目录失败: {e}")
            return False

    yaml_loader = YAML()
    yaml_loader.preserve_quotes = True
    yaml_loader.indent(mapping=2, sequence=4, offset=2)

    try:
        data_to_save = None
        if os.path.exists(filename) and os.path.getsize(filename) > 0:
            try:
                with open(filename, "r", encoding="utf-8-sig") as f:
                    data_to_save = yaml_loader.load(f)
                if not isinstance(data_to_save, dict):
                    logger.warning(f"日志配置文件 {filename} 内容格式不正确，将使用新配置覆盖。")
                    data_to_save = None
            except Exception as e:
                logger.error(f"读取日志配置文件 {filename} 失败: {e}。将尝试使用新配置。")
                data_to_save = None

        if data_to_save is None:
            logger.info(f"将从 DEFAULT_LOG_CONFIG_TEXT 创建或覆盖日志配置: {filename}")
            try:
                default_log_config_stream = io.StringIO(DEFAULT_LOG_CONFIG_TEXT)
                data_to_save = yaml_loader.load(default_log_config_stream)
                if not isinstance(data_to_save, dict):
                     logger.error(f"DEFAULT_LOG_CONFIG_TEXT 解析失败。")
                     data_to_save = get_default_log_config_dict()
            except Exception as e_load_default:
                logger.error(f"从 DEFAULT_LOG_CONFIG_TEXT 加载默认日志配置失败: {e_load_default}。")
                data_to_save = get_default_log_config_dict()

        # 合并传入的 log_config_data 到 data_to_save
        if isinstance(data_to_save, dict):
            _merge_ruamel_data(data_to_save, log_config_data)
        else:
            data_to_save = log_config_data

        # 确保目录存在
        os.makedirs(os.path.dirname(filename), exist_ok=True)

        # 使用 utf-8-sig 编码保存文件，添加 BOM 头
        with open(filename, "w", encoding="utf-8-sig") as f:
            yaml_loader.dump(data_to_save, f)

        logger.info(f"日志配置文件已保存: {filename}")
        return True
    except Exception as e:
        logger.error(f"保存日志配置文件 {filename} 失败: {e}", exc_info=True)
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(filename), exist_ok=True)

            with open(filename, 'w', encoding="utf-8-sig") as file:
                yaml.dump(log_config_data, file, allow_unicode=True, default_flow_style=False, sort_keys=False, indent=2)
            logger.info(f"日志配置已通过备用方法 (PyYAML) 保存到 {filename}")
            return True
        except Exception as e2:
            logger.error(f"日志配置备用保存方式 (PyYAML) 也失败: {e2}")
            return False

# --- mode_config.yaml 相关函数 ---
def get_default_mode_config_dict() -> Dict:
    yaml_loader = YAML()
    try:
        config_dict = yaml_loader.load(DEFAULT_MODE_CONFIG_TEXT)
        if not isinstance(config_dict, dict):
            logger.error("DEFAULT_MODE_CONFIG_TEXT 解析失败，返回空字典。")
            return {}
        return config_dict
    except Exception as e:
        logger.error(f"解析 DEFAULT_MODE_CONFIG_TEXT 失败: {e}，返回空字典。")
        return {}

def save_mode_config_file(mode_config_data: Dict, filename: str = MODE_CONFIG_FILE) -> bool:
    # 检查目标路径是否可写
    if not is_path_writable(filename):
        # 尝试使用用户目录作为备用路径
        user_home = os.path.expanduser("~")
        app_data_dir = os.path.join(user_home, ".multitranslator")
        try:
            if not os.path.exists(app_data_dir):
                os.makedirs(app_data_dir)
            backup_filename = os.path.join(app_data_dir, os.path.basename(filename))
            logger.warning(f"无法写入 {filename}，将使用备用路径: {backup_filename}")
            filename = backup_filename
        except Exception as e:
            logger.error(f"创建备用配置目录失败: {e}")
            return False

    yaml_loader = YAML()
    yaml_loader.preserve_quotes = True
    yaml_loader.indent(mapping=2, sequence=4, offset=2)

    try:
        data_to_save = None
        if os.path.exists(filename) and os.path.getsize(filename) > 0:
            try:
                with open(filename, "r", encoding="utf-8-sig") as f:
                    data_to_save = yaml_loader.load(f)
                if not isinstance(data_to_save, dict):
                    logger.warning(f"模式配置文件 {filename} 内容格式不正确，将使用新配置覆盖。")
                    data_to_save = None
            except Exception as e:
                logger.error(f"读取模式配置文件 {filename} 失败: {e}。将尝试使用新配置。")
                data_to_save = None

        if data_to_save is None:
            logger.info(f"将从 DEFAULT_MODE_CONFIG_TEXT 创建或覆盖模式配置: {filename}")
            try:
                default_mode_config_stream = io.StringIO(DEFAULT_MODE_CONFIG_TEXT)
                data_to_save = yaml_loader.load(default_mode_config_stream)
                if not isinstance(data_to_save, dict):
                     logger.error(f"DEFAULT_MODE_CONFIG_TEXT 解析失败。")
                     data_to_save = get_default_mode_config_dict()
            except Exception as e_load_default:
                logger.error(f"从 DEFAULT_MODE_CONFIG_TEXT 加载默认模式配置失败: {e_load_default}。")
                data_to_save = get_default_mode_config_dict()

        # 合并传入的 mode_config_data 到 data_to_save
        if isinstance(data_to_save, dict):
            _merge_ruamel_data(data_to_save, mode_config_data) # 使用 _merge_ruamel_data 进行合并
        else:
            data_to_save = mode_config_data

        # 确保目录存在
        os.makedirs(os.path.dirname(filename), exist_ok=True)

        # 使用 utf-8-sig 编码保存文件，添加 BOM 头
        with open(filename, "w", encoding="utf-8-sig") as f:
            yaml_loader.dump(data_to_save, f)

        logger.info(f"模式配置文件已保存: {filename}")
        return True
    except Exception as e:
        logger.error(f"保存模式配置文件 {filename} 失败: {e}", exc_info=True)
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(filename), exist_ok=True)

            with open(filename, 'w', encoding="utf-8-sig") as file:
                yaml.dump(mode_config_data, file, allow_unicode=True, default_flow_style=False, sort_keys=False, indent=2)
            logger.info(f"模式配置已通过备用方法 (PyYAML) 保存到 {filename}")
            return True
        except Exception as e2:
            logger.error(f"模式配置备用保存方式 (PyYAML) 也失败: {e2}")
            return False

def complete_language_features_and_tones_in_dict(mode_config: Dict) -> Dict:
    # (从主程序复制并适配，确保它直接操作传入的字典)
    required_keys = ['tone_particles', 'language_features', 'special_language_groups', 'special_language_pairs', 'pair_disambiguation_rules']
    for key in required_keys:
        if key not in mode_config:
            mode_config[key] = {}
            logger.debug(f"在mode_config中创建缺失的键: {key}")

    required_langs = set()
    if 'translation_modes' in mode_config and isinstance(mode_config['translation_modes'], dict):
        for mode in mode_config['translation_modes'].values():
            if isinstance(mode, dict):
                source_code = mode.get('source_code')
                target_code = mode.get('target_code')
                if source_code: required_langs.add(source_code)
                if target_code: required_langs.add(target_code)

    logger.debug(f"需要支持的语言代码: {required_langs}")

    # 更新语言特征定义，添加unique_features字段
    default_language_features = {
        # 东亚语言
        "zh": {"pattern": r"[\u4E00-\u9FFF]", "exclusive": [r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "汉字", "question_pattern": r"[?？]", "exclamation_pattern": r"[!！]", "unique_features": ["的", "了", "是", "在", "不", "我", "有", "和", "你", "这"]},
        "ko": {"pattern": r"[\uAC00-\uD7AF]", "exclusive": [r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "韩文谚文", "question_pattern": r"[?？]", "exclamation_pattern": r"[!！]", "unique_features": ["요", "니다", "이다", "음", "군요", "네요", "아요", "어요", "ㅋㅋ", "ㅎㅎ"]},
        "ja": {"pattern": r"[\u3040-\u309F\u30A0-\u30FF]", "exclusive": [r"[\uAC00-\uD7AF]"], "desc": "日文假名", "question_pattern": r"[?？]", "exclamation_pattern": r"[!！]", "unique_features": ["です", "ます", "ござい", "だ", "たい", "ません", "ました", "の", "へ", "を"]},

        # 欧洲语言
        "en": {"pattern": r"[A-Za-z]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "英文拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["the", "and", "of", "to", "a", "in", "is", "you", "that", "it"]},
        "fr": {"pattern": r"[A-Za-zÀ-ÿ]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "法文拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["les", "des", "est", "dans", "pour", "pas", "mais", "avec", "sont", "vous"]},
        "de": {"pattern": r"[A-Za-zÄäÖöÜüß]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "德文拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["der", "die", "das", "und", "ist", "in", "den", "von", "zu", "mit"]},
        "es": {"pattern": r"[A-Za-zÁáÉéÍíÓóÚúÜüÑñ]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "西班牙文拉丁字母", "question_pattern": r"[?¿]", "exclamation_pattern": r"[!¡]", "unique_features": ["el", "la", "los", "las", "que", "en", "y", "es", "para", "por"]},
        "it": {"pattern": r"[A-Za-zÀàÈèÉéÌìÍíÒòÓóÙùÚú]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "意大利文拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["il", "la", "di", "e", "che", "un", "per", "è", "in", "non"]},
        "pt": {"pattern": r"[A-Za-zÁáÂâÃãÀàÇçÉéÊêÍíÓóÔôÕõÚú]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "葡萄牙文拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["o", "a", "e", "de", "em", "para", "com", "não", "um", "uma"]},
        "ru": {"pattern": r"[А-Яа-я]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "俄文西里尔字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["и", "в", "не", "на", "я", "что", "быть", "с", "он", "это"]},
        "nl": {"pattern": r"[A-Za-zÀ-ÿ]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "荷兰语拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["de", "het", "een", "en", "van", "in", "is", "dat", "op", "te"]},
        "sv": {"pattern": r"[A-Za-zÅåÄäÖö]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "瑞典语拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["och", "att", "det", "är", "jag", "i", "en", "på", "som", "inte"]},
        "da": {"pattern": r"[A-Za-zÆæØøÅå]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "丹麦语拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["og", "at", "det", "er", "jeg", "i", "en", "på", "som", "ikke"]},
        "no": {"pattern": r"[A-Za-zÆæØøÅå]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "挪威语拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["og", "å", "det", "er", "jeg", "i", "en", "på", "som", "ikke"]},
        "fi": {"pattern": r"[A-Za-zÄäÖö]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "芬兰语拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["ja", "on", "että", "se", "ei", "olen", "hän", "minä", "sinä", "mitä"]},
        "pl": {"pattern": r"[A-Za-zĄąĆćĘęŁłŃńÓóŚśŹźŻż]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "波兰语拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["i", "w", "na", "nie", "się", "jest", "do", "że", "z", "to"]},
        "cs": {"pattern": r"[A-Za-zÁáČčĎďÉéĚěÍíŇňÓóŘřŠšŤťÚúŮůÝýŽž]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "捷克语拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["a", "v", "je", "na", "se", "to", "že", "s", "z", "jsem"]},

        # 南亚和东南亚语言
        "vi": {"pattern": r"[A-Za-zÀ-ỹ]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "越南文拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["của", "là", "và", "một", "có", "những", "được", "không", "trong", "để"]},
        "ar": {"pattern": r"[\u0600-\u06FF]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "阿拉伯文字母", "question_pattern": r"[؟]", "exclamation_pattern": r"[!]", "unique_features": ["في", "من", "على", "إلى", "أن", "هذا", "هذه", "هو", "هي", "أنا"]},
        "th": {"pattern": r"[\u0E00-\u0E7F]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "泰文字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["ที่", "ของ", "และ", "ใน", "จะ", "ไม่", "เป็น", "ได้", "มี", "การ"]},
        "km": {"pattern": r"[\u1780-\u17FF]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "柬埔寨文字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["នៅ", "ក្នុង", "និង", "ការ", "របស់", "ទៅ", "មាន", "ពី", "ដែល", "ជា"]},
        "hi": {"pattern": r"[\u0900-\u097F]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "印地语字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["के", "का", "में", "है", "और", "को", "एक", "यह", "हैं", "से"]},
        "bn": {"pattern": r"[\u0980-\u09FF]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "孟加拉语字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["এবং", "একটি", "করা", "হয়", "আছে", "এই", "না", "কি", "তার", "জন্য"]},
        "ta": {"pattern": r"[\u0B80-\u0BFF]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "泰米尔语字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["மற்றும்", "என்று", "ஒரு", "இந்த", "அது", "என", "இல்லை", "உள்ள", "அவர்", "போன்ற"]},
        "id": {"pattern": r"[A-Za-z]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "印尼语拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["dan", "yang", "di", "dengan", "untuk", "adalah", "tidak", "dari", "ini", "itu"]},
        "ms": {"pattern": r"[A-Za-z]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "马来语拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["dan", "yang", "di", "dengan", "untuk", "adalah", "tidak", "dari", "ini", "itu"]},

        # 中东语言
        "he": {"pattern": r"[\u0590-\u05FF]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "希伯来语字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["של", "את", "הוא", "היא", "אני", "לא", "זה", "עם", "על", "כי"]},
        "tr": {"pattern": r"[A-Za-zÇçĞğİıÖöŞşÜü]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "土耳其语拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["ve", "bir", "bu", "için", "ben", "sen", "o", "biz", "var", "yok"]},
        "ur": {"pattern": r"[\u0600-\u06FF]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "乌尔都语字母", "question_pattern": r"[؟]", "exclamation_pattern": r"[!]", "unique_features": ["کا", "کے", "میں", "اور", "ہے", "کو", "ایک", "سے", "نہیں", "کہ"]},

        # 非洲语言
        "sw": {"pattern": r"[A-Za-z]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "斯瓦希里语拉丁字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["na", "ya", "kwa", "ni", "wa", "katika", "si", "kuwa", "cha", "hii"]},
        "am": {"pattern": r"[\u1200-\u137F]", "exclusive": [r"[\u4E00-\u9FFF]", r"[\uAC00-\uD7AF]", r"[\u3040-\u309F\u30A0-\u30FF]"], "desc": "阿姆哈拉语字母", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": ["የ", "ና", "ለ", "ነው", "እና", "አለ", "ይህ", "እኔ", "አንተ", "ነኝ"]}
    }

    default_tone_particles = {
        # 东亚语言
        "zh": "[哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]",
        "ko": "[ㅋ|ㅎ|아|네|헤|ㅜ]",
        "ja": "[ｗ笑]",

        # 欧洲语言
        "en": "[lol|haha|hehe|yeah]",
        "fr": "[lol|haha|hein|quoi|eh bien]",
        "de": "[haha|hehe|ne|ja|doch|mal]",
        "es": "[jaja|jeje|eh|pues|vale|no]",
        "ru": "[хаха|хехе|ну|да|же]",
        "pt": "[haha|hehe|né|pois|então]",
        "it": "[haha|hehe|eh|beh|dai|cioè]",
        "nl": "[haha|hehe|ja|nou|toch|wel]",
        "sv": "[haha|hehe|ja|ju|väl|nog]",
        "da": "[haha|hehe|ja|jo|vel|nok]",
        "no": "[haha|hehe|ja|jo|vel|nok]",
        "fi": "[haha|hehe|joo|no|kyllä|kai]",
        "pl": "[haha|hehe|tak|no|cóż|więc]",
        "cs": "[haha|hehe|ano|no|tak|teda]",

        # 南亚和东南亚语言
        "vi": "[hihi|hehe|ạ|nhé|nha|á|ấy]",
        "ar": "[هههه|والله|يعني|طيب]",
        "th": "[ฮ่าฮ่า|ฮิฮิ|นะ|ค่ะ|ครับ|จ้า|ล่ะ]",
        "km": "[ហាហា|ហិហិ|ណា|ទេ|ហើយ]",
        "hi": "[हाहा|हेहे|अच्छा|ठीक|हां|यार]",
        "bn": "[হাহা|হেহে|আচ্ছা|ঠিক|হ্যাঁ|যার]",
        "ta": "[ஹாஹா|ஹேஹே|சரி|ஆமாம்|அப்படியா]",
        "id": "[haha|hihi|ya|dong|sih|kok]",
        "ms": "[haha|hehe|ya|lah|kan|pun]",

        # 中东语言
        "he": "[חהחה|חחח|אה|כן|אוי|וואו]",
        "tr": "[haha|hehe|ya|işte|evet|yani]",
        "ur": "[ہاہا|ہیہی|اچھا|ٹھیک|ہاں|یار]",

        # 非洲语言
        "sw": "[haha|hehe|ndiyo|sawa|basi|haya]",
        "am": "[ሃሃ|ሄሄ|አዎ|እሺ|እና]"
    }

    lang_features_map = mode_config.setdefault('language_features', {})
    tone_particles_map = mode_config.setdefault('tone_particles', {})

    for lang in required_langs:
        if lang not in lang_features_map:
            if lang in default_language_features:
                lang_features_map[lang] = default_language_features[lang].copy()
            else:
                lang_features_map[lang] = {"pattern": r"[\w\p{L}]", "exclusive": [], "desc": f"{lang}语言字符", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": []}
            logger.debug(f"为语言 {lang} 添加默认/通用语言特征配置")
        else: # 确保现有配置完整
            feature = lang_features_map[lang]
            defaults = default_language_features.get(lang, {"pattern": r"[\w\p{L}]", "exclusive": [], "desc": f"{lang}语言字符", "question_pattern": r"[?]", "exclamation_pattern": r"[!]", "unique_features": []})
            for k, v_default in defaults.items():
                feature.setdefault(k, v_default)

        if lang not in tone_particles_map:
            tone_particles_map[lang] = default_tone_particles.get(lang, r"[\w\p{P}]+")
            logger.debug(f"为语言 {lang} 添加默认/通用语气词配置")

    # 添加默认的语言对消歧规则
    pair_disambiguation_rules = mode_config.setdefault('pair_disambiguation_rules', {})

    # 添加常见的语言对消歧规则
    default_disambiguation_rules = {
        # 东亚语言对
        "ko-zh": {
            "enabled": True,
            "ko_specific_ratio_threshold": 0.3,
            "zh_specific_ratio_threshold": 0.3,
            "feature_dominance_ratio": 2.0
        },
        "ja-zh": {
            "enabled": True,
            "ja_specific_ratio_threshold": 0.3,
            "zh_specific_ratio_threshold": 0.3,
            "feature_dominance_ratio": 2.0
        },
        "ja-ko": {
            "enabled": True,
            "ja_specific_ratio_threshold": 0.3,
            "ko_specific_ratio_threshold": 0.3,
            "feature_dominance_ratio": 2.0
        },

        # 欧洲语言对
        "en-fr": {
            "enabled": True,
            "en_specific_ratio_threshold": 0.25,
            "fr_specific_ratio_threshold": 0.25,
            "feature_dominance_ratio": 1.8
        },
        "en-de": {
            "enabled": True,
            "en_specific_ratio_threshold": 0.25,
            "de_specific_ratio_threshold": 0.25,
            "feature_dominance_ratio": 1.8
        },
        "en-es": {
            "enabled": True,
            "en_specific_ratio_threshold": 0.25,
            "es_specific_ratio_threshold": 0.25,
            "feature_dominance_ratio": 1.8
        },
        "fr-es": {
            "enabled": True,
            "fr_specific_ratio_threshold": 0.25,
            "es_specific_ratio_threshold": 0.25,
            "feature_dominance_ratio": 1.8
        },
        "fr-de": {
            "enabled": True,
            "fr_specific_ratio_threshold": 0.25,
            "de_specific_ratio_threshold": 0.25,
            "feature_dominance_ratio": 1.8
        },

        # 跨语族语言对
        "zh-en": {
            "enabled": True,
            "zh_specific_ratio_threshold": 0.3,
            "en_specific_ratio_threshold": 0.3,
            "feature_dominance_ratio": 2.0
        },
        "ru-en": {
            "enabled": True,
            "ru_specific_ratio_threshold": 0.3,
            "en_specific_ratio_threshold": 0.3,
            "feature_dominance_ratio": 2.0
        },
        "ar-en": {
            "enabled": True,
            "ar_specific_ratio_threshold": 0.3,
            "en_specific_ratio_threshold": 0.3,
            "feature_dominance_ratio": 2.0
        },
        "hi-en": {
            "enabled": True,
            "hi_specific_ratio_threshold": 0.3,
            "en_specific_ratio_threshold": 0.3,
            "feature_dominance_ratio": 2.0
        },
        "th-en": {
            "enabled": True,
            "th_specific_ratio_threshold": 0.3,
            "en_specific_ratio_threshold": 0.3,
            "feature_dominance_ratio": 2.0
        },

        # 东南亚语言对
        "th-vi": {
            "enabled": True,
            "th_specific_ratio_threshold": 0.3,
            "vi_specific_ratio_threshold": 0.3,
            "feature_dominance_ratio": 1.8
        },
        "id-ms": {
            "enabled": True,
            "id_specific_ratio_threshold": 0.15, # 这两种语言非常相似，需要降低阈值
            "ms_specific_ratio_threshold": 0.15,
            "feature_dominance_ratio": 1.5
        },

        # 南亚语言对
        "hi-bn": {
            "enabled": True,
            "hi_specific_ratio_threshold": 0.25,
            "bn_specific_ratio_threshold": 0.25,
            "feature_dominance_ratio": 1.8
        },
        "hi-ta": {
            "enabled": True,
            "hi_specific_ratio_threshold": 0.25,
            "ta_specific_ratio_threshold": 0.25,
            "feature_dominance_ratio": 1.8
        },

        # 中东语言对
        "ar-he": {
            "enabled": True,
            "ar_specific_ratio_threshold": 0.3,
            "he_specific_ratio_threshold": 0.3,
            "feature_dominance_ratio": 2.0
        },
        "ar-ur": {
            "enabled": True,
            "ar_specific_ratio_threshold": 0.2, # 这两种语言共享阿拉伯字母，需要降低阈值
            "ur_specific_ratio_threshold": 0.2,
            "feature_dominance_ratio": 1.5
        }
    }

    # 添加缺失的默认规则
    for rule_key, rule_config in default_disambiguation_rules.items():
        if rule_key not in pair_disambiguation_rules:
            pair_disambiguation_rules[rule_key] = rule_config
            logger.debug(f"添加默认语言对消歧规则: {rule_key}")

    # 为其他所有需要的语言对添加基本消歧规则
    for lang1 in required_langs:
        for lang2 in required_langs:
            if lang1 != lang2:
                pair_key = f"{lang1}-{lang2}"
                reverse_key = f"{lang2}-{lang1}"

                # 避免重复添加
                if pair_key not in pair_disambiguation_rules and reverse_key not in pair_disambiguation_rules:
                    # 添加基本消歧规则
                    pair_disambiguation_rules[pair_key] = {
                        "enabled": True,
                        f"{lang1}_specific_ratio_threshold": 0.3,
                        f"{lang2}_specific_ratio_threshold": 0.3,
                        "feature_dominance_ratio": 2.0
                    }
                    logger.debug(f"添加基本语言对消歧规则: {pair_key}")

    # 继续原有的语言组配置代码
    default_language_groups = {
        "cjk": {"languages": ["zh", "ja", "ko"], "strict_detection": False, "desc": "中日韩语言组"},
        "latin": {"languages": ["en", "fr", "es", "de", "it", "pt", "nl", "sv", "da", "no", "fi", "vi"], "strict_detection": False, "desc": "拉丁语系"},
        "slavic": {"languages": ["be", "bs", "hr", "sl", "sr", "bg", "ru", "cs", "sk", "mk", "pl", "uk"], "strict_detection": False, "desc": "斯拉夫语系"},
        "indic": {"languages": ["ml", "mr", "ta", "te", "bn", "gu", "kn", "hi", "pa"], "strict_detection": False, "desc": "印度语系"},
        "semitic": {"languages": ["he", "am", "ti", "mt", "ar"], "strict_detection": False, "desc": "闪米特语系"},
        "southeast_asian": {"languages": ["vi", "th", "lo", "km", "my", "id", "ms", "tl"], "strict_detection": False, "desc": "东南亚语系"},
        "germanic": {"languages": ["en", "de", "nl", "sv", "da", "no"], "strict_detection": False, "desc": "日耳曼语族"},
        "romance": {"languages": ["fr", "es", "it", "pt", "ro", "ca"], "strict_detection": False, "desc": "罗曼语族"},
        "baltic": {"languages": ["lt", "lv"], "strict_detection": False, "desc": "波罗的海语族"},
        "uralic": {"languages": ["fi", "et", "hu"], "strict_detection": False, "desc": "乌拉尔语族"},
        "turkic": {"languages": ["tr", "az", "kk", "ky", "uz"], "strict_detection": False, "desc": "突厥语族"},
        "dravidian": {"languages": ["ta", "te", "ml", "kn"], "strict_detection": False, "desc": "德拉维族语系"},
        "sino_tibetan": {"languages": ["zh", "bo", "my"], "strict_detection": False, "desc": "汉藏语系"},
        "austronesian": {"languages": ["id", "ms", "tl", "jv"], "strict_detection": False, "desc": "南岛语系"},
        "afro_asiatic": {"languages": ["ar", "he", "am", "ha"], "strict_detection": False, "desc": "亚非语系"},
        "tai_kadai": {"languages": ["th", "lo"], "strict_detection": False, "desc": "壮侗语系"},
        "austroasiatic": {"languages": ["vi", "km"], "strict_detection": False, "desc": "南亚语系"}
    }

    special_groups_map = mode_config.setdefault('special_language_groups', {})
    for group_name, group_config in default_language_groups.items():
        if group_name not in special_groups_map:
            special_groups_map[group_name] = group_config.copy()
        else: # 合并 languages 列表，并确保其他键存在
            existing_group = special_groups_map[group_name]
            existing_languages = set(existing_group.get("languages", []))
            new_languages = set(group_config.get("languages", []))
            existing_group["languages"] = list(existing_languages.union(new_languages))
            existing_group.setdefault("strict_detection", group_config.get("strict_detection", False))
            existing_group.setdefault("desc", group_config.get("desc", f"{group_name} 语言组"))


    special_pairs_map = mode_config.setdefault('special_language_pairs', {})
    if "*-*" not in special_pairs_map:
        special_pairs_map["*-*"] = {"max_attempts": 2, "desc": "通用语言对配置", "skip_source_detection": False, "min_char_ratio": 0.2}

    # 添加其他语言对配置
    default_special_pairs = {
        # 东亚语言对配置
        "zh-ko": {"max_attempts": 2, "desc": "zh-ko互译配置", "skip_source_detection": True, "min_char_ratio": 0.3},
        "zh-ja": {"max_attempts": 2, "desc": "zh-ja互译配置", "skip_source_detection": True, "min_char_ratio": 0.3},
        "ja-ko": {"max_attempts": 2, "desc": "ja-ko互译配置", "skip_source_detection": True, "min_char_ratio": 0.3},

        # 亚欧语言对配置
        "zh-en": {"max_attempts": 2, "desc": "zh-en互译配置", "skip_source_detection": False, "min_char_ratio": 0.15, "allow_short_text_mismatch": True},
        "zh-vi": {"max_attempts": 2, "desc": "zh-vi互译配置", "skip_source_detection": False, "min_char_ratio": 0.15, "allow_short_text_mismatch": True},
        "zh-ru": {"max_attempts": 2, "desc": "zh-ru互译配置", "skip_source_detection": False, "min_char_ratio": 0.15, "allow_short_text_mismatch": True},
        "zh-fr": {"max_attempts": 2, "desc": "zh-fr互译配置", "skip_source_detection": False, "min_char_ratio": 0.15, "allow_short_text_mismatch": True},
        "ko-en": {"max_attempts": 2, "desc": "ko-en互译配置", "skip_source_detection": False, "min_char_ratio": 0.15, "allow_short_text_mismatch": True},
        "ja-en": {"max_attempts": 2, "desc": "ja-en互译配置", "skip_source_detection": False, "min_char_ratio": 0.15, "allow_short_text_mismatch": True},

        # 欧洲语言对配置
        "en-fr": {"max_attempts": 2, "desc": "en-fr互译配置", "skip_source_detection": False, "min_char_ratio": 0.3, "check_tone": True},
        "en-de": {"max_attempts": 2, "desc": "en-de互译配置", "skip_source_detection": False, "min_char_ratio": 0.3, "check_tone": True},
        "en-es": {"max_attempts": 2, "desc": "en-es互译配置", "skip_source_detection": False, "min_char_ratio": 0.3, "check_tone": True},
        "en-ru": {"max_attempts": 2, "desc": "en-ru互译配置", "skip_source_detection": False, "min_char_ratio": 0.25, "allow_short_text_mismatch": True},
        "fr-es": {"max_attempts": 2, "desc": "fr-es互译配置", "skip_source_detection": False, "min_char_ratio": 0.4, "check_tone": True},
        "fr-de": {"max_attempts": 2, "desc": "fr-de互译配置", "skip_source_detection": False, "min_char_ratio": 0.4, "check_tone": True},

        # 南亚和东南亚语言对配置
        "th-en": {"max_attempts": 2, "desc": "th-en互译配置", "skip_source_detection": False, "min_char_ratio": 0.15, "allow_short_text_mismatch": True},
        "vi-en": {"max_attempts": 2, "desc": "vi-en互译配置", "skip_source_detection": False, "min_char_ratio": 0.2, "check_tone": True},
        "id-en": {"max_attempts": 2, "desc": "id-en互译配置", "skip_source_detection": False, "min_char_ratio": 0.3, "check_tone": True},
        "hi-en": {"max_attempts": 2, "desc": "hi-en互译配置", "skip_source_detection": False, "min_char_ratio": 0.15, "allow_short_text_mismatch": True},
        "th-vi": {"max_attempts": 2, "desc": "th-vi互译配置", "skip_source_detection": False, "min_char_ratio": 0.15, "allow_short_text_mismatch": True},
        "id-ms": {"max_attempts": 2, "desc": "id-ms互译配置", "skip_source_detection": True, "min_char_ratio": 0.4, "check_tone": True},

        # 中东语言对配置
        "ar-en": {"max_attempts": 2, "desc": "ar-en互译配置", "skip_source_detection": False, "min_char_ratio": 0.15, "allow_short_text_mismatch": True},
        "he-en": {"max_attempts": 2, "desc": "he-en互译配置", "skip_source_detection": False, "min_char_ratio": 0.15, "allow_short_text_mismatch": True},
        "ar-he": {"max_attempts": 2, "desc": "ar-he互译配置", "skip_source_detection": False, "min_char_ratio": 0.3, "check_tone": True},

        # 语言族间配置
        "cjk-european": {"max_attempts": 2, "desc": "东亚-欧洲语言互译配置", "skip_source_detection": False, "min_char_ratio": 0.15, "allow_short_text_mismatch": True},
        "european-southeast_asian": {"max_attempts": 2, "desc": "欧洲-东南亚语言互译配置", "skip_source_detection": False, "min_char_ratio": 0.15, "allow_short_text_mismatch": True},
        "european-indic": {"max_attempts": 2, "desc": "欧洲-南亚语言互译配置", "skip_source_detection": False, "min_char_ratio": 0.15, "allow_short_text_mismatch": True},
        "european-semitic": {"max_attempts": 2, "desc": "欧洲-闪米特语言互译配置", "skip_source_detection": False, "min_char_ratio": 0.15, "allow_short_text_mismatch": True}
    }

    for pair_key, pair_config in default_special_pairs.items():
        if pair_key not in special_pairs_map:
            special_pairs_map[pair_key] = pair_config.copy()

    # 简化 special_language_pairs 的补全逻辑，主要确保定义过的 translation_modes 有基础配置
    if 'translation_modes' in mode_config and isinstance(mode_config['translation_modes'], dict):
        for mode_info in mode_config['translation_modes'].values():
             if isinstance(mode_info, dict):
                source = mode_info.get("source_code")
                target = mode_info.get("target_code")
                if source and target and source != target:
                    pair_key = f"{source}-{target}"
                    if pair_key not in special_pairs_map:
                        source_in_cjk = source in special_groups_map.get('cjk', {}).get('languages', [])
                        target_in_cjk = target in special_groups_map.get('cjk', {}).get('languages', [])

                        default_pair_conf = {"max_attempts": 1, "desc": f"{source}-{target}互译配置", "skip_source_detection": False, "min_char_ratio": 0.1}
                        if source_in_cjk and target_in_cjk:
                            default_pair_conf.update({"skip_source_detection": True, "min_char_ratio": 0.3, "max_attempts": 2})
                        elif source_in_cjk or target_in_cjk:
                            default_pair_conf.update({"min_char_ratio": 0.15, "allow_short_text_mismatch": True, "max_attempts": 2})

                        special_pairs_map[pair_key] = default_pair_conf
                        logger.debug(f"为语言对 {pair_key} 添加默认特殊配置")
    return mode_config


def generate_default_mode_config(force_overwrite: bool = False):
    # 检查目标路径是否可写
    if not is_path_writable(MODE_CONFIG_FILE):
        user_home = os.path.expanduser("~")
        app_data_dir = os.path.join(user_home, ".multitranslator")
        try:
            os.makedirs(app_data_dir, exist_ok=True)
            backup_mode_config_file = os.path.join(app_data_dir, os.path.basename(MODE_CONFIG_FILE))
            logger.warning(f"无法写入 {MODE_CONFIG_FILE}，将使用备用路径: {backup_mode_config_file}")

            # 如果存在备用文件且不强制覆盖，则直接返回
            if not force_overwrite and os.path.exists(backup_mode_config_file):
                logger.info(f"备用模式配置文件 {backup_mode_config_file} 已存在。如需覆盖，请使用 --force 参数。")
                return True

            default_mode_data = get_default_mode_config_dict()
            if not default_mode_data:
                logger.error("无法获取默认模式配置内容，生成失败。")
                return False

            # 在保存前，确保其经过补全逻辑
            completed_mode_data = complete_language_features_and_tones_in_dict(default_mode_data)

            if save_mode_config_file(completed_mode_data, backup_mode_config_file):
                logger.info(f"默认模式配置文件已成功生成到备用位置: {backup_mode_config_file}")
                return True
            else:
                logger.error(f"无法在备用位置生成默认模式配置文件")
                return False
        except Exception as e:
            logger.error(f"创建备用模式配置失败: {e}")
            return False

    if not force_overwrite and os.path.exists(MODE_CONFIG_FILE):
        logger.info(f"{MODE_CONFIG_FILE} 已存在。如需覆盖，请使用 --force 参数。")
        return False

    logger.info(f"正在生成默认模式配置文件: {MODE_CONFIG_FILE}")
    default_mode_data = get_default_mode_config_dict()
    if not default_mode_data:
        logger.error("无法获取默认模式配置内容，生成失败。")
        return False

    # 在保存前，确保其经过补全逻辑
    completed_mode_data = complete_language_features_and_tones_in_dict(default_mode_data)

    if save_mode_config_file(completed_mode_data, MODE_CONFIG_FILE):
        logger.info(f"默认模式配置文件已成功生成: {MODE_CONFIG_FILE}")
        return True
    else:
        logger.error(f"生成默认模式配置文件失败: {MODE_CONFIG_FILE}")
        return False

def generate_default_log_config(force_overwrite: bool = False):
    """生成默认日志配置文件"""
    # 检查目标路径是否可写
    if not is_path_writable(LOG_CONFIG_FILE):
        user_home = os.path.expanduser("~")
        app_data_dir = os.path.join(user_home, ".multitranslator")
        try:
            os.makedirs(app_data_dir, exist_ok=True)
            backup_log_config_file = os.path.join(app_data_dir, os.path.basename(LOG_CONFIG_FILE))
            logger.warning(f"无法写入 {LOG_CONFIG_FILE}，将使用备用路径: {backup_log_config_file}")

            # 如果存在备用文件且不强制覆盖，则直接返回
            if not force_overwrite and os.path.exists(backup_log_config_file):
                logger.info(f"备用日志配置文件 {backup_log_config_file} 已存在。如需覆盖，请使用 --force 参数。")
                return True

            default_log_data = get_default_log_config_dict()
            if not default_log_data:
                logger.error("无法获取默认日志配置内容，生成失败。")
                return False

            if save_log_config_file(default_log_data, backup_log_config_file):
                logger.info(f"默认日志配置文件已成功生成到备用位置: {backup_log_config_file}")
                return True
            else:
                logger.error(f"无法在备用位置生成默认日志配置文件")
                return False
        except Exception as e:
            logger.error(f"创建备用日志配置失败: {e}")
            return False

    if not force_overwrite and os.path.exists(LOG_CONFIG_FILE):
        logger.info(f"{LOG_CONFIG_FILE} 已存在。如需覆盖，请使用 --force 参数。")
        return False

    logger.info(f"正在生成默认日志配置文件: {LOG_CONFIG_FILE}")
    default_log_data = get_default_log_config_dict()
    if not default_log_data:
        logger.error("无法获取默认日志配置内容，生成失败。")
        return False

    if save_log_config_file(default_log_data, LOG_CONFIG_FILE):
        logger.info(f"默认日志配置文件已成功生成: {LOG_CONFIG_FILE}")
        return True
    else:
        logger.error(f"生成默认日志配置文件失败: {LOG_CONFIG_FILE}")
        return False

def prompt_for_api_key() -> str:
    """提示用户输入API密钥并进行加密

    Returns:
        str: 加密后的API密钥
    """
    print("\n请输入加密后的API密钥（将被保存到配置文件）：")
    print("注意：本程序只接受加密格式的API密钥，请使用api_crypto.py工具进行加密。")

    while True:
        api_key = input("请输入加密后的API密钥（输入后按回车）：").strip()
        if not api_key:
            logger.warning("API密钥不能为空，请重新输入")
            continue

        # 初始化API加密对象
        try:
            from api_crypto import ApiCrypto
            api_crypto = ApiCrypto()
            # 验证是否为加密格式
            if api_crypto.is_encrypted(api_key):
                # 尝试解密以验证有效性
                decrypted_key = api_crypto.decrypt(api_key)
                if decrypted_key:
                    logger.info("API密钥验证成功")
                    return api_key
                else:
                    logger.error("加密API密钥无效或无法解密，请重新输入")
            else:
                logger.error("输入的不是有效的加密API密钥，请使用api_crypto.py工具进行加密后再输入")
        except ImportError:
            logger.error("找不到api_crypto模块，无法验证API密钥。请确保api_crypto.py文件在同一目录下")
            if input("是否继续使用未验证的API密钥？(y/n): ").lower() == 'y':
                return api_key
        except Exception as e:
            logger.error(f"验证API密钥时出错: {e}")
            if input("是否继续使用未验证的API密钥？(y/n): ").lower() == 'y':
                return api_key

# 主函数：处理命令行参数
def main():
    """主函数，处理命令行参数并执行相应操作"""
    import argparse

    parser = argparse.ArgumentParser(description="配置文件管理工具")
    parser.add_argument("--generate-config", action="store_true", help="生成默认配置文件")
    parser.add_argument("--generate-mode", action="store_true", help="生成默认模式配置文件")
    parser.add_argument("--generate-log", action="store_true", help="生成默认日志配置文件")
    parser.add_argument("--generate-all", action="store_true", help="生成所有默认配置文件")
    parser.add_argument("--force", action="store_true", help="强制覆盖现有配置文件")
    parser.add_argument("--prompt-api-key", action="store_true", help="提示输入API密钥")

    args = parser.parse_args()

    if not (args.generate_config or args.generate_mode or args.generate_log or args.generate_all or args.prompt_api_key):
        parser.print_help()
        return

    # 生成所有配置
    if args.generate_all:
        args.generate_config = True
        args.generate_mode = True
        args.generate_log = True

    # 生成配置文件
    if args.generate_config:
        config_dict = get_default_config_dict()

        # 处理API密钥
        if args.prompt_api_key:
            api_key = prompt_for_api_key()
            if api_key:
                config_dict["api_key"] = api_key

        generate_default_main_config(force_overwrite=args.force)

    # 生成模式配置文件
    if args.generate_mode:
        generate_default_mode_config(force_overwrite=args.force)

    # 生成日志配置文件
    if args.generate_log:
        generate_default_log_config(force_overwrite=args.force)

if __name__ == "__main__":
    main()